<script>
  let volume = 50;
  let currentStation = "FM 101.5 MHz";
  let isPowered = true;

  // Sample stations for demonstration
  const stations = [
    "FM 101.5 MHz",
    "FM 102.3 MHz",
    "FM 103.7 MHz",
    "FM 104.9 MHz",
    "FM 106.1 MHz",
  ];

  const countries = ["USA", "UK", "Canada", "Australia", "Germany"];

  let currentStationIndex = 0;
  let currentGenreIndex = 0;

  function nextStation() {
    if (!isPowered) return;
    currentStationIndex = (currentStationIndex + 1) % stations.length;
    currentStation = stations[currentStationIndex];
  }

  function prevStation() {
    if (!isPowered) return;
    currentStationIndex =
      currentStationIndex === 0 ? stations.length - 1 : currentStationIndex - 1;
    currentStation = stations[currentStationIndex];
  }

  function nextGenre() {
    if (!isPowered) return;
    currentGenreIndex = (currentGenreIndex + 1) % countries.length;
  }

  function prevGenre() {
    if (!isPowered) return;
    currentGenreIndex =
      currentGenreIndex === 0 ? countries.length - 1 : currentGenreIndex - 1;
  }

  function togglePower() {
    isPowered = !isPowered;
    if (!isPowered) {
      currentStation = "---";
    } else {
      currentStation = stations[currentStationIndex];
    }
  }
</script>

<div class="radio-container">
  <div class="display" class:powered={isPowered}>
    {isPowered ? currentStation : "---"}
  </div>

  <div class="volume-section">
    <div class="volume-label">VOLUME</div>
    <input
      type="range"
      class="volume-slider"
      min="0"
      max="100"
      bind:value={volume}
      disabled={!isPowered}
    />
  </div>

  <div class="controls-section">
    <div class="section-label">Genre</div>
    <div class="button-group">
      <button
        class="control-button"
        title="Previous Genre"
        on:click={prevGenre}
        disabled={!isPowered}>◀</button
      >
      <button
        class="control-button"
        title="Next Genre"
        on:click={nextGenre}
        disabled={!isPowered}>▶</button
      >
    </div>
  </div>

  <div class="controls-section">
    <div class="section-label">Channel</div>
    <div class="button-group">
      <button
        class="control-button"
        title="Previous Channel"
        on:click={prevStation}
        disabled={!isPowered}>◀</button
      >
      <button
        class="control-button"
        title="Next Channel"
        on:click={nextStation}
        disabled={!isPowered}>▶</button
      >
    </div>
  </div>

  <button
    class="power-button"
    class:powered={isPowered}
    title="Power"
    on:click={togglePower}>⏻</button
  >
</div>

<style>
  .radio-container {
    width: 420px;
    height: 620px;
    background: linear-gradient(145deg, #f0f0f0, #d8d8d8);
    border-radius: 35px;
    box-shadow:
      2px 2px 25px rgba(0, 0, 0, 0.15),
      -2px -2px 25px rgba(255, 255, 255, 0.9),
      inset 2px 2px 4px rgba(255, 255, 255, 0.3),
      inset -2px -2px 4px rgba(0, 0, 0, 0.05);
    padding: 40px 35px 50px 35px;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .radio-container::before {
    content: "";
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
  }

  .display {
    width: 100%;
    height: 90px;
    background: linear-gradient(145deg, #0a0a0a, #1a1a1a);
    border-radius: 20px;
    margin-bottom: 35px;
    box-shadow:
      inset 10px 10px 20px rgba(0, 0, 0, 0.5),
      inset -10px -10px 20px rgba(255, 255, 255, 0.02),
      0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 20px;
    font-weight: 600;
    position: relative;
    border: 2px solid #333;
    transition: all 0.3s ease;
  }

  .display.powered {
    color: #00ff88;
    text-shadow:
      0 0 15px #00ff88,
      0 0 30px rgba(0, 255, 136, 0.3);
  }

  .display::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        transparent 49%,
        rgba(255, 255, 255, 0.02) 50%,
        transparent 51%
      ),
      radial-gradient(
        ellipse at top left,
        rgba(255, 255, 255, 0.05) 0%,
        transparent 50%
      );
    border-radius: 18px;
    pointer-events: none;
  }

  .display::after {
    content: "";
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 255, 136, 0.1),
      transparent
    );
    border-radius: 1px;
  }

  .controls-section {
    margin-bottom: 25px;
  }

  .volume-section {
    margin-bottom: 35px;
  }

  .volume-label {
    font-size: 13px;
    color: #777;
    margin-bottom: 15px;
    text-align: center;
    font-weight: 700;
    letter-spacing: 2px;
  }

  .volume-slider {
    width: 100%;
    height: 8px;
    background: #d0d0d0;
    border-radius: 20px;
    outline: none;
    box-shadow:
      inset 4px 4px 8px rgba(0, 0, 0, 0.1),
      inset -4px -4px 8px rgba(255, 255, 255, 0.8);
    -webkit-appearance: none;
    appearance: none;
    transition: opacity 0.3s ease;
  }

  .volume-slider:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 28px;
    height: 28px;
    background: linear-gradient(145deg, #f0f0f0, #d8d8d8);
    border-radius: 50%;
    cursor: pointer;
    box-shadow:
      6px 6px 12px rgba(0, 0, 0, 0.2),
      -6px -6px 12px rgba(255, 255, 255, 0.9),
      inset 2px 2px 4px rgba(255, 255, 255, 0.3),
      inset -2px -2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
  }

  .volume-slider::-webkit-slider-thumb:hover {
    background: linear-gradient(145deg, #f5f5f5, #e0e0e0);
    transform: scale(1.05);
  }

  .button-group {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 25px;
  }

  .control-button {
    width: 70px;
    height: 70px;
    background: linear-gradient(145deg, #f0f0f0, #d8d8d8);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    color: #555;
    box-shadow:
      10px 10px 20px rgba(0, 0, 0, 0.15),
      -10px -10px 20px rgba(255, 255, 255, 0.9),
      inset 2px 2px 4px rgba(255, 255, 255, 0.3),
      inset -2px -2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
  }

  .control-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .control-button::before {
    content: "";
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
  }

  .control-button:hover:not(:disabled) {
    color: #333;
    background: linear-gradient(145deg, #f5f5f5, #e0e0e0);
  }

  .control-button:active:not(:disabled) {
    box-shadow:
      inset 6px 6px 12px rgba(0, 0, 0, 0.2),
      inset -6px -6px 12px rgba(255, 255, 255, 0.9);
    transform: translateY(0);
  }

  .power-button {
    width: 90px;
    height: 90px;
    background: linear-gradient(145deg, #ff4444, #cc3333);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 28px;
    color: white;
    box-shadow:
      12px 12px 24px rgba(0, 0, 0, 0.25),
      -12px -12px 24px rgba(255, 255, 255, 0.8),
      inset 2px 2px 4px rgba(255, 255, 255, 0.2),
      inset -2px -2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    margin-top: 25px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
  }

  .power-button.powered {
    background: linear-gradient(145deg, #ff4444, #cc3333);
    box-shadow:
      12px 12px 24px rgba(0, 0, 0, 0.25),
      -12px -12px 24px rgba(255, 255, 255, 0.8),
      inset 2px 2px 4px rgba(255, 255, 255, 0.2),
      inset -2px -2px 4px rgba(0, 0, 0, 0.2),
      0 0 20px rgba(255, 68, 68, 0.6),
      0 0 40px rgba(255, 68, 68, 0.3),
      0 0 60px rgba(255, 68, 68, 0.1);
  }

  .power-button::before {
    content: "";
    position: absolute;
    top: 4px;
    left: 4px;
    right: 4px;
    bottom: 4px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
  }

  .power-button:hover {
    background: linear-gradient(145deg, #ff5555, #dd4444);
    box-shadow:
      15px 15px 30px rgba(0, 0, 0, 0.3),
      -15px -15px 30px rgba(255, 255, 255, 0.8),
      inset 2px 2px 4px rgba(255, 255, 255, 0.2),
      inset -2px -2px 4px rgba(0, 0, 0, 0.2);
  }

  .power-button.powered:hover {
    background: linear-gradient(145deg, #ff5555, #dd4444);
    box-shadow:
      15px 15px 30px rgba(0, 0, 0, 0.3),
      -15px -15px 30px rgba(255, 255, 255, 0.8),
      inset 2px 2px 4px rgba(255, 255, 255, 0.2),
      inset -2px -2px 4px rgba(0, 0, 0, 0.2),
      0 0 25px rgba(255, 68, 68, 0.8),
      0 0 50px rgba(255, 68, 68, 0.4),
      0 0 75px rgba(255, 68, 68, 0.2);
  }

  .power-button:active {
    box-shadow:
      inset 8px 8px 16px rgba(0, 0, 0, 0.4),
      inset -8px -8px 16px rgba(255, 255, 255, 0.1);
    transform: translateY(0);
  }

  .section-label {
    font-size: 11px;
    color: #999;
    text-align: center;
    margin-bottom: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
  }
</style>
