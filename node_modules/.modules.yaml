hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sveltejs/acorn-typescript@1.0.5(acorn@8.14.1)':
    '@sveltejs/acorn-typescript': private
  '@sveltejs/vite-plugin-svelte-inspector@4.0.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.33.10)(vite@6.3.5))(svelte@5.33.10)(vite@6.3.5)':
    '@sveltejs/vite-plugin-svelte-inspector': private
  '@types/estree@1.0.7':
    '@types/estree': private
  acorn@8.14.1:
    acorn: private
  aria-query@5.3.2:
    aria-query: private
  axobject-query@4.1.0:
    axobject-query: private
  clsx@2.1.1:
    clsx: private
  debug@4.4.1:
    debug: private
  deepmerge@4.3.1:
    deepmerge: private
  esbuild@0.25.5:
    esbuild: private
  esm-env@1.2.2:
    esm-env: private
  esrap@1.4.6:
    esrap: private
  fdir@6.4.5(picomatch@4.0.2):
    fdir: private
  fsevents@2.3.3:
    fsevents: private
  is-reference@3.0.3:
    is-reference: private
  kleur@4.1.5:
    kleur: private
  locate-character@3.0.0:
    locate-character: private
  magic-string@0.30.17:
    magic-string: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.4:
    postcss: private
  rollup@4.41.1:
    rollup: private
  source-map-js@1.2.1:
    source-map-js: private
  tinyglobby@0.2.14:
    tinyglobby: private
  vitefu@1.0.6(vite@6.3.5):
    vitefu: private
  zimmerframe@1.1.2:
    zimmerframe: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.12.1
pendingBuilds: []
prunedAt: Thu, 29 May 2025 22:46:21 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@rollup/rollup-win32-x64-msvc@4.41.1'
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
