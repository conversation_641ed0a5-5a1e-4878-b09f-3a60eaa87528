{"version": 3, "sources": ["../../node_modules/.pnpm/esm-env@1.2.2/node_modules/esm-env/true.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/shared/utils.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/constants.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/reactivity/equality.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/errors.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/flags/index.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/constants.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dev/tracing.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/shared/errors.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/context.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/proxy.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/reactivity/deriveds.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/reactivity/sources.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/warnings.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/hydration.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dev/equality.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/operations.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/reactivity/effects.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/task.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/runtime.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/utils.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/elements/events.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/blocks/svelte-head.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/reconciler.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/template.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/render.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/blocks/snippet.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/shared/attributes.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/elements/attributes.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/elements/bindings/size.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/reactivity/store.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/legacy/legacy-client.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/internal/client/dom/elements/custom-element.js", "../../node_modules/.pnpm/svelte@5.33.10/node_modules/svelte/src/index-client.js"], "sourcesContent": ["export default true;\n", "// Store the references to globals in case someone tries to monkey patch these, causing the below\n// to de-opt (this occurs often when using popular extensions).\nexport var is_array = Array.isArray;\nexport var index_of = Array.prototype.indexOf;\nexport var array_from = Array.from;\nexport var object_keys = Object.keys;\nexport var define_property = Object.defineProperty;\nexport var get_descriptor = Object.getOwnPropertyDescriptor;\nexport var get_descriptors = Object.getOwnPropertyDescriptors;\nexport var object_prototype = Object.prototype;\nexport var array_prototype = Array.prototype;\nexport var get_prototype_of = Object.getPrototypeOf;\nexport var is_extensible = Object.isExtensible;\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nexport function is_function(thing) {\n\treturn typeof thing === 'function';\n}\n\nexport const noop = () => {};\n\n// Adapted from https://github.com/then/is-promise/blob/master/index.js\n// Distributed under MIT License https://github.com/then/is-promise/blob/master/LICENSE\n\n/**\n * @template [T=any]\n * @param {any} value\n * @returns {value is PromiseLike<T>}\n */\nexport function is_promise(value) {\n\treturn typeof value?.then === 'function';\n}\n\n/** @param {Function} fn */\nexport function run(fn) {\n\treturn fn();\n}\n\n/** @param {Array<() => void>} arr */\nexport function run_all(arr) {\n\tfor (var i = 0; i < arr.length; i++) {\n\t\tarr[i]();\n\t}\n}\n\n/**\n * TODO replace with Promise.withResolvers once supported widely enough\n * @template T\n */\nexport function deferred() {\n\t/** @type {(value: T) => void} */\n\tvar resolve;\n\n\t/** @type {(reason: any) => void} */\n\tvar reject;\n\n\t/** @type {Promise<T>} */\n\tvar promise = new Promise((res, rej) => {\n\t\tresolve = res;\n\t\treject = rej;\n\t});\n\n\t// @ts-expect-error\n\treturn { promise, resolve, reject };\n}\n\n/**\n * @template V\n * @param {V} value\n * @param {V | (() => V)} fallback\n * @param {boolean} [lazy]\n * @returns {V}\n */\nexport function fallback(value, fallback, lazy = false) {\n\treturn value === undefined\n\t\t? lazy\n\t\t\t? /** @type {() => V} */ (fallback)()\n\t\t\t: /** @type {V} */ (fallback)\n\t\t: value;\n}\n\n/**\n * When encountering a situation like `let [a, b, c] = $derived(blah())`,\n * we need to stash an intermediate value that `a`, `b`, and `c` derive\n * from, in case it's an iterable\n * @template T\n * @param {ArrayLike<T> | Iterable<T>} value\n * @param {number} [n]\n * @returns {Array<T>}\n */\nexport function to_array(value, n) {\n\t// return arrays unchanged\n\tif (Array.isArray(value)) {\n\t\treturn value;\n\t}\n\n\t// if value is not iterable, or `n` is unspecified (indicates a rest\n\t// element, which means we're not concerned about unbounded iterables)\n\t// convert to an array with `Array.from`\n\tif (n === undefined || !(Symbol.iterator in value)) {\n\t\treturn Array.from(value);\n\t}\n\n\t// otherwise, populate an array with `n` values\n\n\t/** @type {T[]} */\n\tconst array = [];\n\n\tfor (const element of value) {\n\t\tarray.push(element);\n\t\tif (array.length === n) break;\n\t}\n\n\treturn array;\n}\n", "export const DERIVED = 1 << 1;\nexport const EFFECT = 1 << 2;\nexport const RENDER_EFFECT = 1 << 3;\nexport const BLOCK_EFFECT = 1 << 4;\nexport const BRANCH_EFFECT = 1 << 5;\nexport const ROOT_EFFECT = 1 << 6;\nexport const BOUNDARY_EFFECT = 1 << 7;\nexport const UNOWNED = 1 << 8;\nexport const DISCONNECTED = 1 << 9;\nexport const CLEAN = 1 << 10;\nexport const DIRTY = 1 << 11;\nexport const MAYBE_DIRTY = 1 << 12;\nexport const INERT = 1 << 13;\nexport const DESTROYED = 1 << 14;\nexport const EFFECT_RAN = 1 << 15;\n/** 'Transparent' effects do not create a transition boundary */\nexport const EFFECT_TRANSPARENT = 1 << 16;\n/** Svelte 4 legacy mode props need to be handled with deriveds and be recognized elsewhere, hence the dedicated flag */\nexport const LEGACY_DERIVED_PROP = 1 << 17;\nexport const INSPECT_EFFECT = 1 << 18;\nexport const HEAD_EFFECT = 1 << 19;\nexport const EFFECT_HAS_DERIVED = 1 << 20;\nexport const EFFECT_IS_UPDATING = 1 << 21;\n\nexport const STATE_SYMBOL = Symbol('$state');\nexport const LEGACY_PROPS = Symbol('legacy props');\nexport const LOADING_ATTR_SYMBOL = Symbol('');\n", "/** @import { Equals } from '#client' */\n\n/** @type {Equals} */\nexport function equals(value) {\n\treturn value === this.v;\n}\n\n/**\n * @param {unknown} a\n * @param {unknown} b\n * @returns {boolean}\n */\nexport function safe_not_equal(a, b) {\n\treturn a != a\n\t\t? b == b\n\t\t: a !== b || (a !== null && typeof a === 'object') || typeof a === 'function';\n}\n\n/**\n * @param {unknown} a\n * @param {unknown} b\n * @returns {boolean}\n */\nexport function not_equal(a, b) {\n\treturn a !== b;\n}\n\n/** @type {Equals} */\nexport function safe_equals(value) {\n\treturn !safe_not_equal(value, this.v);\n}\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\n/**\n * Using `bind:value` together with a checkbox input is not allowed. Use `bind:checked` instead\n * @returns {never}\n */\nexport function bind_invalid_checkbox_value() {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_invalid_checkbox_value\\nUsing \\`bind:value\\` together with a checkbox input is not allowed. Use \\`bind:checked\\` instead\\nhttps://svelte.dev/e/bind_invalid_checkbox_value`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_invalid_checkbox_value`);\n\t}\n}\n\n/**\n * Component %component% has an export named `%key%` that a consumer component is trying to access using `bind:%key%`, which is disallowed. Instead, use `bind:this` (e.g. `<%name% bind:this={component} />`) and then access the property on the bound component instance (e.g. `component.%key%`)\n * @param {string} component\n * @param {string} key\n * @param {string} name\n * @returns {never}\n */\nexport function bind_invalid_export(component, key, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_invalid_export\\nComponent ${component} has an export named \\`${key}\\` that a consumer component is trying to access using \\`bind:${key}\\`, which is disallowed. Instead, use \\`bind:this\\` (e.g. \\`<${name} bind:this={component} />\\`) and then access the property on the bound component instance (e.g. \\`component.${key}\\`)\\nhttps://svelte.dev/e/bind_invalid_export`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_invalid_export`);\n\t}\n}\n\n/**\n * A component is attempting to bind to a non-bindable property `%key%` belonging to %component% (i.e. `<%name% bind:%key%={...}>`). To mark a property as bindable: `let { %key% = $bindable() } = $props()`\n * @param {string} key\n * @param {string} component\n * @param {string} name\n * @returns {never}\n */\nexport function bind_not_bindable(key, component, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`bind_not_bindable\\nA component is attempting to bind to a non-bindable property \\`${key}\\` belonging to ${component} (i.e. \\`<${name} bind:${key}={...}>\\`). To mark a property as bindable: \\`let { ${key} = $bindable() } = $props()\\`\\nhttps://svelte.dev/e/bind_not_bindable`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/bind_not_bindable`);\n\t}\n}\n\n/**\n * Calling `%method%` on a component instance (of %component%) is no longer valid in Svelte 5\n * @param {string} method\n * @param {string} component\n * @returns {never}\n */\nexport function component_api_changed(method, component) {\n\tif (DEV) {\n\t\tconst error = new Error(`component_api_changed\\nCalling \\`${method}\\` on a component instance (of ${component}) is no longer valid in Svelte 5\\nhttps://svelte.dev/e/component_api_changed`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/component_api_changed`);\n\t}\n}\n\n/**\n * Attempted to instantiate %component% with `new %name%`, which is no longer valid in Svelte 5. If this component is not under your control, set the `compatibility.componentApi` compiler option to `4` to keep it working.\n * @param {string} component\n * @param {string} name\n * @returns {never}\n */\nexport function component_api_invalid_new(component, name) {\n\tif (DEV) {\n\t\tconst error = new Error(`component_api_invalid_new\\nAttempted to instantiate ${component} with \\`new ${name}\\`, which is no longer valid in Svelte 5. If this component is not under your control, set the \\`compatibility.componentApi\\` compiler option to \\`4\\` to keep it working.\\nhttps://svelte.dev/e/component_api_invalid_new`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/component_api_invalid_new`);\n\t}\n}\n\n/**\n * A derived value cannot reference itself recursively\n * @returns {never}\n */\nexport function derived_references_self() {\n\tif (DEV) {\n\t\tconst error = new Error(`derived_references_self\\nA derived value cannot reference itself recursively\\nhttps://svelte.dev/e/derived_references_self`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/derived_references_self`);\n\t}\n}\n\n/**\n * Keyed each block has duplicate key `%value%` at indexes %a% and %b%\n * @param {string} a\n * @param {string} b\n * @param {string | undefined | null} [value]\n * @returns {never}\n */\nexport function each_key_duplicate(a, b, value) {\n\tif (DEV) {\n\t\tconst error = new Error(`each_key_duplicate\\n${value ? `Keyed each block has duplicate key \\`${value}\\` at indexes ${a} and ${b}` : `Keyed each block has duplicate key at indexes ${a} and ${b}`}\\nhttps://svelte.dev/e/each_key_duplicate`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/each_key_duplicate`);\n\t}\n}\n\n/**\n * `%rune%` cannot be used inside an effect cleanup function\n * @param {string} rune\n * @returns {never}\n */\nexport function effect_in_teardown(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_in_teardown\\n\\`${rune}\\` cannot be used inside an effect cleanup function\\nhttps://svelte.dev/e/effect_in_teardown`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_in_teardown`);\n\t}\n}\n\n/**\n * Effect cannot be created inside a `$derived` value that was not itself created inside an effect\n * @returns {never}\n */\nexport function effect_in_unowned_derived() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_in_unowned_derived\\nEffect cannot be created inside a \\`$derived\\` value that was not itself created inside an effect\\nhttps://svelte.dev/e/effect_in_unowned_derived`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_in_unowned_derived`);\n\t}\n}\n\n/**\n * `%rune%` can only be used inside an effect (e.g. during component initialisation)\n * @param {string} rune\n * @returns {never}\n */\nexport function effect_orphan(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_orphan\\n\\`${rune}\\` can only be used inside an effect (e.g. during component initialisation)\\nhttps://svelte.dev/e/effect_orphan`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_orphan`);\n\t}\n}\n\n/**\n * Maximum update depth exceeded. This can happen when a reactive block or effect repeatedly sets a new value. Svelte limits the number of nested updates to prevent infinite loops\n * @returns {never}\n */\nexport function effect_update_depth_exceeded() {\n\tif (DEV) {\n\t\tconst error = new Error(`effect_update_depth_exceeded\\nMaximum update depth exceeded. This can happen when a reactive block or effect repeatedly sets a new value. Svelte limits the number of nested updates to prevent infinite loops\\nhttps://svelte.dev/e/effect_update_depth_exceeded`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/effect_update_depth_exceeded`);\n\t}\n}\n\n/**\n * Failed to hydrate the application\n * @returns {never}\n */\nexport function hydration_failed() {\n\tif (DEV) {\n\t\tconst error = new Error(`hydration_failed\\nFailed to hydrate the application\\nhttps://svelte.dev/e/hydration_failed`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/hydration_failed`);\n\t}\n}\n\n/**\n * Could not `{@render}` snippet due to the expression being `null` or `undefined`. Consider using optional chaining `{@render snippet?.()}`\n * @returns {never}\n */\nexport function invalid_snippet() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_snippet\\nCould not \\`{@render}\\` snippet due to the expression being \\`null\\` or \\`undefined\\`. Consider using optional chaining \\`{@render snippet?.()}\\`\\nhttps://svelte.dev/e/invalid_snippet`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_snippet`);\n\t}\n}\n\n/**\n * `%name%(...)` cannot be used in runes mode\n * @param {string} name\n * @returns {never}\n */\nexport function lifecycle_legacy_only(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`lifecycle_legacy_only\\n\\`${name}(...)\\` cannot be used in runes mode\\nhttps://svelte.dev/e/lifecycle_legacy_only`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/lifecycle_legacy_only`);\n\t}\n}\n\n/**\n * Cannot do `bind:%key%={undefined}` when `%key%` has a fallback value\n * @param {string} key\n * @returns {never}\n */\nexport function props_invalid_value(key) {\n\tif (DEV) {\n\t\tconst error = new Error(`props_invalid_value\\nCannot do \\`bind:${key}={undefined}\\` when \\`${key}\\` has a fallback value\\nhttps://svelte.dev/e/props_invalid_value`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/props_invalid_value`);\n\t}\n}\n\n/**\n * Rest element properties of `$props()` such as `%property%` are readonly\n * @param {string} property\n * @returns {never}\n */\nexport function props_rest_readonly(property) {\n\tif (DEV) {\n\t\tconst error = new Error(`props_rest_readonly\\nRest element properties of \\`$props()\\` such as \\`${property}\\` are readonly\\nhttps://svelte.dev/e/props_rest_readonly`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/props_rest_readonly`);\n\t}\n}\n\n/**\n * The `%rune%` rune is only available inside `.svelte` and `.svelte.js/ts` files\n * @param {string} rune\n * @returns {never}\n */\nexport function rune_outside_svelte(rune) {\n\tif (DEV) {\n\t\tconst error = new Error(`rune_outside_svelte\\nThe \\`${rune}\\` rune is only available inside \\`.svelte\\` and \\`.svelte.js/ts\\` files\\nhttps://svelte.dev/e/rune_outside_svelte`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/rune_outside_svelte`);\n\t}\n}\n\n/**\n * Property descriptors defined on `$state` objects must contain `value` and always be `enumerable`, `configurable` and `writable`.\n * @returns {never}\n */\nexport function state_descriptors_fixed() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_descriptors_fixed\\nProperty descriptors defined on \\`$state\\` objects must contain \\`value\\` and always be \\`enumerable\\`, \\`configurable\\` and \\`writable\\`.\\nhttps://svelte.dev/e/state_descriptors_fixed`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_descriptors_fixed`);\n\t}\n}\n\n/**\n * Cannot set prototype of `$state` object\n * @returns {never}\n */\nexport function state_prototype_fixed() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_prototype_fixed\\nCannot set prototype of \\`$state\\` object\\nhttps://svelte.dev/e/state_prototype_fixed`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_prototype_fixed`);\n\t}\n}\n\n/**\n * Updating state inside a derived or a template expression is forbidden. If the value should not be reactive, declare it without `$state`\n * @returns {never}\n */\nexport function state_unsafe_mutation() {\n\tif (DEV) {\n\t\tconst error = new Error(`state_unsafe_mutation\\nUpdating state inside a derived or a template expression is forbidden. If the value should not be reactive, declare it without \\`$state\\`\\nhttps://svelte.dev/e/state_unsafe_mutation`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/state_unsafe_mutation`);\n\t}\n}", "export let legacy_mode_flag = false;\nexport let tracing_mode_flag = false;\n\nexport function enable_legacy_mode_flag() {\n\tlegacy_mode_flag = true;\n}\n\nexport function enable_tracing_mode_flag() {\n\ttracing_mode_flag = true;\n}\n", "export const EACH_ITEM_REACTIVE = 1;\nexport const EACH_INDEX_REACTIVE = 1 << 1;\n/** See EachBlock interface metadata.is_controlled for an explanation what this is */\nexport const EACH_IS_CONTROLLED = 1 << 2;\nexport const EACH_IS_ANIMATED = 1 << 3;\nexport const EACH_ITEM_IMMUTABLE = 1 << 4;\n\nexport const PROPS_IS_IMMUTABLE = 1;\nexport const PROPS_IS_RUNES = 1 << 1;\nexport const PROPS_IS_UPDATED = 1 << 2;\nexport const PROPS_IS_BINDABLE = 1 << 3;\nexport const PROPS_IS_LAZY_INITIAL = 1 << 4;\n\nexport const TRANSITION_IN = 1;\nexport const TRANSITION_OUT = 1 << 1;\nexport const TRANSITION_GLOBAL = 1 << 2;\n\nexport const TEMPLATE_FRAGMENT = 1;\nexport const TEMPLATE_USE_IMPORT_NODE = 1 << 1;\nexport const TEMPLATE_USE_SVG = 1 << 2;\nexport const TEMPLATE_USE_MATHML = 1 << 3;\n\nexport const HYDRATION_START = '[';\n/** used to indicate that an `{:else}...` block was rendered */\nexport const HYDRATION_START_ELSE = '[!';\nexport const HYDRATION_END = ']';\nexport const HYDRATION_ERROR = {};\n\nexport const ELEMENT_IS_NAMESPACED = 1;\nexport const ELEMENT_PRESERVE_ATTRIBUTE_CASE = 1 << 1;\n\nexport const UNINITIALIZED = Symbol();\n\n// Dev-time component properties\nexport const FILENAME = Symbol('filename');\nexport const HMR = Symbol('hmr');\n\nexport const NAMESPACE_HTML = 'http://www.w3.org/1999/xhtml';\nexport const NAMESPACE_SVG = 'http://www.w3.org/2000/svg';\nexport const NAMESPACE_MATHML = 'http://www.w3.org/1998/Math/MathML';\n\n// we use a list of ignorable runtime warnings because not every runtime warning\n// can be ignored and we want to keep the validation for svelte-ignore in place\nexport const IGNORABLE_RUNTIME_WARNINGS = /** @type {const} */ ([\n\t'state_snapshot_uncloneable',\n\t'binding_property_non_reactive',\n\t'hydration_attribute_changed',\n\t'hydration_html_changed',\n\t'ownership_invalid_binding',\n\t'ownership_invalid_mutation'\n]);\n\n/**\n * Whitespace inside one of these elements will not result in\n * a whitespace node being created in any circumstances. (This\n * list is almost certainly very incomplete)\n * TODO this is currently unused\n */\nexport const ELEMENTS_WITHOUT_TEXT = ['audio', 'datalist', 'dl', 'optgroup', 'select', 'video'];\n\nexport const ATTACHMENT_KEY = '@attach';\n", "/** @import { Derived, Reaction, Value } from '#client' */\nimport { UNINITIALIZED } from '../../../constants.js';\nimport { snapshot } from '../../shared/clone.js';\nimport { define_property } from '../../shared/utils.js';\nimport { DERIVED, STATE_SYMBOL } from '#client/constants';\nimport { effect_tracking } from '../reactivity/effects.js';\nimport { active_reaction, captured_signals, set_captured_signals, untrack } from '../runtime.js';\n\n/** @type { any } */\nexport let tracing_expressions = null;\n\n/**\n * @param { Value } signal\n * @param { { read: Error[] } } [entry]\n */\nfunction log_entry(signal, entry) {\n\tconst debug = signal.debug;\n\tconst value = signal.trace_need_increase ? signal.trace_v : signal.v;\n\n\tif (value === UNINITIALIZED) {\n\t\treturn;\n\t}\n\n\tif (debug) {\n\t\tvar previous_captured_signals = captured_signals;\n\t\tvar captured = new Set();\n\t\tset_captured_signals(captured);\n\t\ttry {\n\t\t\tuntrack(() => {\n\t\t\t\tdebug();\n\t\t\t});\n\t\t} finally {\n\t\t\tset_captured_signals(previous_captured_signals);\n\t\t}\n\t\tif (captured.size > 0) {\n\t\t\tfor (const dep of captured) {\n\t\t\t\tlog_entry(dep);\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\t}\n\n\tconst type = (signal.f & DERIVED) !== 0 ? '$derived' : '$state';\n\tconst current_reaction = /** @type {Reaction} */ (active_reaction);\n\tconst dirty = signal.wv > current_reaction.wv || current_reaction.wv === 0;\n\n\t// eslint-disable-next-line no-console\n\tconsole.groupCollapsed(\n\t\t`%c${type}`,\n\t\tdirty ? 'color: CornflowerBlue; font-weight: bold' : 'color: grey; font-weight: bold',\n\t\ttypeof value === 'object' && value !== null && STATE_SYMBOL in value\n\t\t\t? snapshot(value, true)\n\t\t\t: value\n\t);\n\n\tif (type === '$derived') {\n\t\tconst deps = new Set(/** @type {Derived} */ (signal).deps);\n\t\tfor (const dep of deps) {\n\t\t\tlog_entry(dep);\n\t\t}\n\t}\n\n\tif (signal.created) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.log(signal.created);\n\t}\n\n\tif (signal.updated) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.log(signal.updated);\n\t}\n\n\tconst read = entry?.read;\n\n\tif (read && read.length > 0) {\n\t\tfor (var stack of read) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(stack);\n\t\t}\n\t}\n\n\t// eslint-disable-next-line no-console\n\tconsole.groupEnd();\n}\n\n/**\n * @template T\n * @param {() => string} label\n * @param {() => T} fn\n */\nexport function trace(label, fn) {\n\tvar previously_tracing_expressions = tracing_expressions;\n\ttry {\n\t\ttracing_expressions = { entries: new Map(), reaction: active_reaction };\n\n\t\tvar start = performance.now();\n\t\tvar value = fn();\n\t\tvar time = (performance.now() - start).toFixed(2);\n\n\t\tif (!effect_tracking()) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(`${label()} %cran outside of an effect (${time}ms)`, 'color: grey');\n\t\t} else if (tracing_expressions.entries.size === 0) {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.log(`${label()} %cno reactive dependencies (${time}ms)`, 'color: grey');\n\t\t} else {\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.group(`${label()} %c(${time}ms)`, 'color: grey');\n\n\t\t\tvar entries = tracing_expressions.entries;\n\n\t\t\ttracing_expressions = null;\n\n\t\t\tfor (const [signal, entry] of entries) {\n\t\t\t\tlog_entry(signal, entry);\n\t\t\t}\n\t\t\t// eslint-disable-next-line no-console\n\t\t\tconsole.groupEnd();\n\t\t}\n\n\t\tif (previously_tracing_expressions !== null && tracing_expressions !== null) {\n\t\t\tfor (const [signal, entry] of tracing_expressions.entries) {\n\t\t\t\tvar prev_entry = previously_tracing_expressions.get(signal);\n\n\t\t\t\tif (prev_entry === undefined) {\n\t\t\t\t\tpreviously_tracing_expressions.set(signal, entry);\n\t\t\t\t} else {\n\t\t\t\t\tprev_entry.read.push(...entry.read);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn value;\n\t} finally {\n\t\ttracing_expressions = previously_tracing_expressions;\n\t}\n}\n\n/**\n * @param {string} label\n */\nexport function get_stack(label) {\n\tlet error = Error();\n\tconst stack = error.stack;\n\n\tif (stack) {\n\t\tconst lines = stack.split('\\n');\n\t\tconst new_lines = ['\\n'];\n\n\t\tfor (let i = 0; i < lines.length; i++) {\n\t\t\tconst line = lines[i];\n\n\t\t\tif (line === 'Error') {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (line.includes('validate_each_keys')) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tif (line.includes('svelte/src/internal')) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tnew_lines.push(line);\n\t\t}\n\n\t\tif (new_lines.length === 1) {\n\t\t\treturn null;\n\t\t}\n\n\t\tdefine_property(error, 'stack', {\n\t\t\tvalue: new_lines.join('\\n')\n\t\t});\n\n\t\tdefine_property(error, 'name', {\n\t\t\t// 'Error' suffix is required for stack traces to be rendered properly\n\t\t\tvalue: `${label}Error`\n\t\t});\n\t}\n\treturn error;\n}\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\n/**\n * Cannot use `{@render children(...)}` if the parent component uses `let:` directives. Consider using a named snippet instead\n * @returns {never}\n */\nexport function invalid_default_snippet() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_default_snippet\\nCannot use \\`{@render children(...)}\\` if the parent component uses \\`let:\\` directives. Consider using a named snippet instead\\nhttps://svelte.dev/e/invalid_default_snippet`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_default_snippet`);\n\t}\n}\n\n/**\n * A snippet function was passed invalid arguments. Snippets should only be instantiated via `{@render ...}`\n * @returns {never}\n */\nexport function invalid_snippet_arguments() {\n\tif (DEV) {\n\t\tconst error = new Error(`invalid_snippet_arguments\\nA snippet function was passed invalid arguments. Snippets should only be instantiated via \\`{@render ...}\\`\\nhttps://svelte.dev/e/invalid_snippet_arguments`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/invalid_snippet_arguments`);\n\t}\n}\n\n/**\n * `%name%(...)` can only be used during component initialisation\n * @param {string} name\n * @returns {never}\n */\nexport function lifecycle_outside_component(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`lifecycle_outside_component\\n\\`${name}(...)\\` can only be used during component initialisation\\nhttps://svelte.dev/e/lifecycle_outside_component`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/lifecycle_outside_component`);\n\t}\n}\n\n/**\n * Attempted to render a snippet without a `{@render}` block. This would cause the snippet code to be stringified instead of its content being rendered to the DOM. To fix this, change `{snippet}` to `{@render snippet()}`.\n * @returns {never}\n */\nexport function snippet_without_render_tag() {\n\tif (DEV) {\n\t\tconst error = new Error(`snippet_without_render_tag\\nAttempted to render a snippet without a \\`{@render}\\` block. This would cause the snippet code to be stringified instead of its content being rendered to the DOM. To fix this, change \\`{snippet}\\` to \\`{@render snippet()}\\`.\\nhttps://svelte.dev/e/snippet_without_render_tag`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/snippet_without_render_tag`);\n\t}\n}\n\n/**\n * `%name%` is not a store with a `subscribe` method\n * @param {string} name\n * @returns {never}\n */\nexport function store_invalid_shape(name) {\n\tif (DEV) {\n\t\tconst error = new Error(`store_invalid_shape\\n\\`${name}\\` is not a store with a \\`subscribe\\` method\\nhttps://svelte.dev/e/store_invalid_shape`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/store_invalid_shape`);\n\t}\n}\n\n/**\n * The `this` prop on `<svelte:element>` must be a string, if defined\n * @returns {never}\n */\nexport function svelte_element_invalid_this_value() {\n\tif (DEV) {\n\t\tconst error = new Error(`svelte_element_invalid_this_value\\nThe \\`this\\` prop on \\`<svelte:element>\\` must be a string, if defined\\nhttps://svelte.dev/e/svelte_element_invalid_this_value`);\n\n\t\terror.name = 'Svelte error';\n\t\tthrow error;\n\t} else {\n\t\tthrow new Error(`https://svelte.dev/e/svelte_element_invalid_this_value`);\n\t}\n}", "/** @import { ComponentContext } from '#client' */\n\nimport { DEV } from 'esm-env';\nimport { lifecycle_outside_component } from '../shared/errors.js';\nimport { source } from './reactivity/sources.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from './runtime.js';\nimport { effect, teardown } from './reactivity/effects.js';\nimport { legacy_mode_flag } from '../flags/index.js';\n\n/** @type {ComponentContext | null} */\nexport let component_context = null;\n\n/** @param {ComponentContext | null} context */\nexport function set_component_context(context) {\n\tcomponent_context = context;\n}\n\n/**\n * The current component function. Different from current component context:\n * ```html\n * <!-- App.svelte -->\n * <Foo>\n *   <Bar /> <!-- context == Foo.svelte, function == App.svelte -->\n * </Foo>\n * ```\n * @type {ComponentContext['function']}\n */\nexport let dev_current_component_function = null;\n\n/** @param {ComponentContext['function']} fn */\nexport function set_dev_current_component_function(fn) {\n\tdev_current_component_function = fn;\n}\n\n/**\n * Retrieves the context that belongs to the closest parent component with the specified `key`.\n * Must be called during component initialisation.\n *\n * @template T\n * @param {any} key\n * @returns {T}\n */\nexport function getContext(key) {\n\tconst context_map = get_or_init_context_map('getContext');\n\tconst result = /** @type {T} */ (context_map.get(key));\n\treturn result;\n}\n\n/**\n * Associates an arbitrary `context` object with the current component and the specified `key`\n * and returns that object. The context is then available to children of the component\n * (including slotted content) with `getContext`.\n *\n * Like lifecycle functions, this must be called during component initialisation.\n *\n * @template T\n * @param {any} key\n * @param {T} context\n * @returns {T}\n */\nexport function setContext(key, context) {\n\tconst context_map = get_or_init_context_map('setContext');\n\tcontext_map.set(key, context);\n\treturn context;\n}\n\n/**\n * Checks whether a given `key` has been set in the context of a parent component.\n * Must be called during component initialisation.\n *\n * @param {any} key\n * @returns {boolean}\n */\nexport function hasContext(key) {\n\tconst context_map = get_or_init_context_map('hasContext');\n\treturn context_map.has(key);\n}\n\n/**\n * Retrieves the whole context map that belongs to the closest parent component.\n * Must be called during component initialisation. Useful, for example, if you\n * programmatically create a component and want to pass the existing context to it.\n *\n * @template {Map<any, any>} [T=Map<any, any>]\n * @returns {T}\n */\nexport function getAllContexts() {\n\tconst context_map = get_or_init_context_map('getAllContexts');\n\treturn /** @type {T} */ (context_map);\n}\n\n/**\n * @param {Record<string, unknown>} props\n * @param {any} runes\n * @param {Function} [fn]\n * @returns {void}\n */\nexport function push(props, runes = false, fn) {\n\tvar ctx = (component_context = {\n\t\tp: component_context,\n\t\tc: null,\n\t\td: false,\n\t\te: null,\n\t\tm: false,\n\t\ts: props,\n\t\tx: null,\n\t\tl: null\n\t});\n\n\tif (legacy_mode_flag && !runes) {\n\t\tcomponent_context.l = {\n\t\t\ts: null,\n\t\t\tu: null,\n\t\t\tr1: [],\n\t\t\tr2: source(false)\n\t\t};\n\t}\n\n\tteardown(() => {\n\t\t/** @type {ComponentContext} */ (ctx).d = true;\n\t});\n\n\tif (DEV) {\n\t\t// component function\n\t\tcomponent_context.function = fn;\n\t\tdev_current_component_function = fn;\n\t}\n}\n\n/**\n * @template {Record<string, any>} T\n * @param {T} [component]\n * @returns {T}\n */\nexport function pop(component) {\n\tconst context_stack_item = component_context;\n\tif (context_stack_item !== null) {\n\t\tif (component !== undefined) {\n\t\t\tcontext_stack_item.x = component;\n\t\t}\n\t\tconst component_effects = context_stack_item.e;\n\t\tif (component_effects !== null) {\n\t\t\tvar previous_effect = active_effect;\n\t\t\tvar previous_reaction = active_reaction;\n\t\t\tcontext_stack_item.e = null;\n\t\t\ttry {\n\t\t\t\tfor (var i = 0; i < component_effects.length; i++) {\n\t\t\t\t\tvar component_effect = component_effects[i];\n\t\t\t\t\tset_active_effect(component_effect.effect);\n\t\t\t\t\tset_active_reaction(component_effect.reaction);\n\t\t\t\t\teffect(component_effect.fn);\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tset_active_effect(previous_effect);\n\t\t\t\tset_active_reaction(previous_reaction);\n\t\t\t}\n\t\t}\n\t\tcomponent_context = context_stack_item.p;\n\t\tif (DEV) {\n\t\t\tdev_current_component_function = context_stack_item.p?.function ?? null;\n\t\t}\n\t\tcontext_stack_item.m = true;\n\t}\n\t// Micro-optimization: Don't set .a above to the empty object\n\t// so it can be garbage-collected when the return here is unused\n\treturn component || /** @type {T} */ ({});\n}\n\n/** @returns {boolean} */\nexport function is_runes() {\n\treturn !legacy_mode_flag || (component_context !== null && component_context.l === null);\n}\n\n/**\n * @param {string} name\n * @returns {Map<unknown, unknown>}\n */\nfunction get_or_init_context_map(name) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component(name);\n\t}\n\n\treturn (component_context.c ??= new Map(get_parent_context(component_context) || undefined));\n}\n\n/**\n * @param {ComponentContext} component_context\n * @returns {Map<unknown, unknown> | null}\n */\nfunction get_parent_context(component_context) {\n\tlet parent = component_context.p;\n\twhile (parent !== null) {\n\t\tconst context_map = parent.c;\n\t\tif (context_map !== null) {\n\t\t\treturn context_map;\n\t\t}\n\t\tparent = parent.p;\n\t}\n\treturn null;\n}\n", "/** @import { Source } from '#client' */\nimport { DEV } from 'esm-env';\nimport { get, active_effect, active_reaction, set_active_reaction } from './runtime.js';\nimport {\n\tarray_prototype,\n\tget_descriptor,\n\tget_prototype_of,\n\tis_array,\n\tobject_prototype\n} from '../shared/utils.js';\nimport { state as source, set } from './reactivity/sources.js';\nimport { STATE_SYMBOL } from '#client/constants';\nimport { UNINITIALIZED } from '../../constants.js';\nimport * as e from './errors.js';\nimport { get_stack } from './dev/tracing.js';\nimport { tracing_mode_flag } from '../flags/index.js';\n\n/**\n * @template T\n * @param {T} value\n * @returns {T}\n */\nexport function proxy(value) {\n\t// if non-proxyable, or is already a proxy, return `value`\n\tif (typeof value !== 'object' || value === null || STATE_SYMBOL in value) {\n\t\treturn value;\n\t}\n\n\tconst prototype = get_prototype_of(value);\n\n\tif (prototype !== object_prototype && prototype !== array_prototype) {\n\t\treturn value;\n\t}\n\n\t/** @type {Map<any, Source<any>>} */\n\tvar sources = new Map();\n\tvar is_proxied_array = is_array(value);\n\tvar version = source(0);\n\n\tvar stack = DEV && tracing_mode_flag ? get_stack('CreatedAt') : null;\n\tvar reaction = active_reaction;\n\n\t/**\n\t * @template T\n\t * @param {() => T} fn\n\t */\n\tvar with_parent = (fn) => {\n\t\tvar previous_reaction = active_reaction;\n\t\tset_active_reaction(reaction);\n\n\t\t/** @type {T} */\n\t\tvar result = fn();\n\n\t\tset_active_reaction(previous_reaction);\n\t\treturn result;\n\t};\n\n\tif (is_proxied_array) {\n\t\t// We need to create the length source eagerly to ensure that\n\t\t// mutations to the array are properly synced with our proxy\n\t\tsources.set('length', source(/** @type {any[]} */ (value).length, stack));\n\t}\n\n\treturn new Proxy(/** @type {any} */ (value), {\n\t\tdefineProperty(_, prop, descriptor) {\n\t\t\tif (\n\t\t\t\t!('value' in descriptor) ||\n\t\t\t\tdescriptor.configurable === false ||\n\t\t\t\tdescriptor.enumerable === false ||\n\t\t\t\tdescriptor.writable === false\n\t\t\t) {\n\t\t\t\t// we disallow non-basic descriptors, because unless they are applied to the\n\t\t\t\t// target object — which we avoid, so that state can be forked — we will run\n\t\t\t\t// afoul of the various invariants\n\t\t\t\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy/Proxy/getOwnPropertyDescriptor#invariants\n\t\t\t\te.state_descriptors_fixed();\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\n\t\t\tif (s === undefined) {\n\t\t\t\ts = with_parent(() => source(descriptor.value, stack));\n\t\t\t\tsources.set(prop, s);\n\t\t\t} else {\n\t\t\t\tset(\n\t\t\t\t\ts,\n\t\t\t\t\twith_parent(() => proxy(descriptor.value))\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tdeleteProperty(target, prop) {\n\t\t\tvar s = sources.get(prop);\n\n\t\t\tif (s === undefined) {\n\t\t\t\tif (prop in target) {\n\t\t\t\t\tsources.set(\n\t\t\t\t\t\tprop,\n\t\t\t\t\t\twith_parent(() => source(UNINITIALIZED, stack))\n\t\t\t\t\t);\n\t\t\t\t\tupdate_version(version);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// When working with arrays, we need to also ensure we update the length when removing\n\t\t\t\t// an indexed property\n\t\t\t\tif (is_proxied_array && typeof prop === 'string') {\n\t\t\t\t\tvar ls = /** @type {Source<number>} */ (sources.get('length'));\n\t\t\t\t\tvar n = Number(prop);\n\n\t\t\t\t\tif (Number.isInteger(n) && n < ls.v) {\n\t\t\t\t\t\tset(ls, n);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tset(s, UNINITIALIZED);\n\t\t\t\tupdate_version(version);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\tget(target, prop, receiver) {\n\t\t\tif (prop === STATE_SYMBOL) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar exists = prop in target;\n\n\t\t\t// create a source, but only if it's an own property and not a prototype property\n\t\t\tif (s === undefined && (!exists || get_descriptor(target, prop)?.writable)) {\n\t\t\t\ts = with_parent(() => source(proxy(exists ? target[prop] : UNINITIALIZED), stack));\n\t\t\t\tsources.set(prop, s);\n\t\t\t}\n\n\t\t\tif (s !== undefined) {\n\t\t\t\tvar v = get(s);\n\t\t\t\treturn v === UNINITIALIZED ? undefined : v;\n\t\t\t}\n\n\t\t\treturn Reflect.get(target, prop, receiver);\n\t\t},\n\n\t\tgetOwnPropertyDescriptor(target, prop) {\n\t\t\tvar descriptor = Reflect.getOwnPropertyDescriptor(target, prop);\n\n\t\t\tif (descriptor && 'value' in descriptor) {\n\t\t\t\tvar s = sources.get(prop);\n\t\t\t\tif (s) descriptor.value = get(s);\n\t\t\t} else if (descriptor === undefined) {\n\t\t\t\tvar source = sources.get(prop);\n\t\t\t\tvar value = source?.v;\n\n\t\t\t\tif (source !== undefined && value !== UNINITIALIZED) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tenumerable: true,\n\t\t\t\t\t\tconfigurable: true,\n\t\t\t\t\t\tvalue,\n\t\t\t\t\t\twritable: true\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn descriptor;\n\t\t},\n\n\t\thas(target, prop) {\n\t\t\tif (prop === STATE_SYMBOL) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar has = (s !== undefined && s.v !== UNINITIALIZED) || Reflect.has(target, prop);\n\n\t\t\tif (\n\t\t\t\ts !== undefined ||\n\t\t\t\t(active_effect !== null && (!has || get_descriptor(target, prop)?.writable))\n\t\t\t) {\n\t\t\t\tif (s === undefined) {\n\t\t\t\t\ts = with_parent(() => source(has ? proxy(target[prop]) : UNINITIALIZED, stack));\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t}\n\n\t\t\t\tvar value = get(s);\n\t\t\t\tif (value === UNINITIALIZED) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn has;\n\t\t},\n\n\t\tset(target, prop, value, receiver) {\n\t\t\tvar s = sources.get(prop);\n\t\t\tvar has = prop in target;\n\n\t\t\t// variable.length = value -> clear all signals with index >= value\n\t\t\tif (is_proxied_array && prop === 'length') {\n\t\t\t\tfor (var i = value; i < /** @type {Source<number>} */ (s).v; i += 1) {\n\t\t\t\t\tvar other_s = sources.get(i + '');\n\t\t\t\t\tif (other_s !== undefined) {\n\t\t\t\t\t\tset(other_s, UNINITIALIZED);\n\t\t\t\t\t} else if (i in target) {\n\t\t\t\t\t\t// If the item exists in the original, we need to create a uninitialized source,\n\t\t\t\t\t\t// else a later read of the property would result in a source being created with\n\t\t\t\t\t\t// the value of the original item at that index.\n\t\t\t\t\t\tother_s = with_parent(() => source(UNINITIALIZED, stack));\n\t\t\t\t\t\tsources.set(i + '', other_s);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we haven't yet created a source for this property, we need to ensure\n\t\t\t// we do so otherwise if we read it later, then the write won't be tracked and\n\t\t\t// the heuristics of effects will be different vs if we had read the proxied\n\t\t\t// object property before writing to that property.\n\t\t\tif (s === undefined) {\n\t\t\t\tif (!has || get_descriptor(target, prop)?.writable) {\n\t\t\t\t\ts = with_parent(() => source(undefined, stack));\n\t\t\t\t\tset(\n\t\t\t\t\t\ts,\n\t\t\t\t\t\twith_parent(() => proxy(value))\n\t\t\t\t\t);\n\t\t\t\t\tsources.set(prop, s);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thas = s.v !== UNINITIALIZED;\n\t\t\t\tset(\n\t\t\t\t\ts,\n\t\t\t\t\twith_parent(() => proxy(value))\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tvar descriptor = Reflect.getOwnPropertyDescriptor(target, prop);\n\n\t\t\t// Set the new value before updating any signals so that any listeners get the new value\n\t\t\tif (descriptor?.set) {\n\t\t\t\tdescriptor.set.call(receiver, value);\n\t\t\t}\n\n\t\t\tif (!has) {\n\t\t\t\t// If we have mutated an array directly, we might need to\n\t\t\t\t// signal that length has also changed. Do it before updating metadata\n\t\t\t\t// to ensure that iterating over the array as a result of a metadata update\n\t\t\t\t// will not cause the length to be out of sync.\n\t\t\t\tif (is_proxied_array && typeof prop === 'string') {\n\t\t\t\t\tvar ls = /** @type {Source<number>} */ (sources.get('length'));\n\t\t\t\t\tvar n = Number(prop);\n\n\t\t\t\t\tif (Number.isInteger(n) && n >= ls.v) {\n\t\t\t\t\t\tset(ls, n + 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tupdate_version(version);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t},\n\n\t\townKeys(target) {\n\t\t\tget(version);\n\n\t\t\tvar own_keys = Reflect.ownKeys(target).filter((key) => {\n\t\t\t\tvar source = sources.get(key);\n\t\t\t\treturn source === undefined || source.v !== UNINITIALIZED;\n\t\t\t});\n\n\t\t\tfor (var [key, source] of sources) {\n\t\t\t\tif (source.v !== UNINITIALIZED && !(key in target)) {\n\t\t\t\t\town_keys.push(key);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn own_keys;\n\t\t},\n\n\t\tsetPrototypeOf() {\n\t\t\te.state_prototype_fixed();\n\t\t}\n\t});\n}\n\n/**\n * @param {Source<number>} signal\n * @param {1 | -1} [d]\n */\nfunction update_version(signal, d = 1) {\n\tset(signal, signal.v + d);\n}\n\n/**\n * @param {any} value\n */\nexport function get_proxied_value(value) {\n\ttry {\n\t\tif (value !== null && typeof value === 'object' && STATE_SYMBOL in value) {\n\t\t\treturn value[STATE_SYMBOL];\n\t\t}\n\t} catch {\n\t\t// the above if check can throw an error if the value in question\n\t\t// is the contentWindow of an iframe on another domain, in which\n\t\t// case we want to just return the value (because it's definitely\n\t\t// not a proxied value) so we don't break any JavaScript interacting\n\t\t// with that iframe (such as various payment companies client side\n\t\t// JavaScript libraries interacting with their iframes on the same\n\t\t// domain)\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {any} a\n * @param {any} b\n */\nexport function is(a, b) {\n\treturn Object.is(get_proxied_value(a), get_proxied_value(b));\n}\n", "/** @import { Derived, Effect } from '#client' */\nimport { DEV } from 'esm-env';\nimport { CLEAN, DERIVED, DIRTY, EFFECT_HAS_DERIVED, MAYBE_DIRTY, UNOWNED } from '#client/constants';\nimport {\n\tactive_reaction,\n\tactive_effect,\n\tset_signal_status,\n\tskip_reaction,\n\tupdate_reaction,\n\tincrement_write_version,\n\tset_active_effect,\n\tpush_reaction_value,\n\tis_destroying_effect\n} from '../runtime.js';\nimport { equals, safe_equals } from './equality.js';\nimport * as e from '../errors.js';\nimport { destroy_effect } from './effects.js';\nimport { inspect_effects, set_inspect_effects } from './sources.js';\nimport { get_stack } from '../dev/tracing.js';\nimport { tracing_mode_flag } from '../../flags/index.js';\nimport { component_context } from '../context.js';\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function derived(fn) {\n\tvar flags = DERIVED | DIRTY;\n\tvar parent_derived =\n\t\tactive_reaction !== null && (active_reaction.f & DERIVED) !== 0\n\t\t\t? /** @type {Derived} */ (active_reaction)\n\t\t\t: null;\n\n\tif (active_effect === null || (parent_derived !== null && (parent_derived.f & UNOWNED) !== 0)) {\n\t\tflags |= UNOWNED;\n\t} else {\n\t\t// Since deriveds are evaluated lazily, any effects created inside them are\n\t\t// created too late to ensure that the parent effect is added to the tree\n\t\tactive_effect.f |= EFFECT_HAS_DERIVED;\n\t}\n\n\t/** @type {Derived<V>} */\n\tconst signal = {\n\t\tctx: component_context,\n\t\tdeps: null,\n\t\teffects: null,\n\t\tequals,\n\t\tf: flags,\n\t\tfn,\n\t\treactions: null,\n\t\trv: 0,\n\t\tv: /** @type {V} */ (null),\n\t\twv: 0,\n\t\tparent: parent_derived ?? active_effect\n\t};\n\n\tif (DEV && tracing_mode_flag) {\n\t\tsignal.created = get_stack('CreatedAt');\n\t}\n\n\treturn signal;\n}\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function user_derived(fn) {\n\tconst d = derived(fn);\n\n\tpush_reaction_value(d);\n\n\treturn d;\n}\n\n/**\n * @template V\n * @param {() => V} fn\n * @returns {Derived<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function derived_safe_equal(fn) {\n\tconst signal = derived(fn);\n\tsignal.equals = safe_equals;\n\treturn signal;\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nexport function destroy_derived_effects(derived) {\n\tvar effects = derived.effects;\n\n\tif (effects !== null) {\n\t\tderived.effects = null;\n\n\t\tfor (var i = 0; i < effects.length; i += 1) {\n\t\t\tdestroy_effect(/** @type {Effect} */ (effects[i]));\n\t\t}\n\t}\n}\n\n/**\n * The currently updating deriveds, used to detect infinite recursion\n * in dev mode and provide a nicer error than 'too much recursion'\n * @type {Derived[]}\n */\nlet stack = [];\n\n/**\n * @param {Derived} derived\n * @returns {Effect | null}\n */\nfunction get_derived_parent_effect(derived) {\n\tvar parent = derived.parent;\n\twhile (parent !== null) {\n\t\tif ((parent.f & DERIVED) === 0) {\n\t\t\treturn /** @type {Effect} */ (parent);\n\t\t}\n\t\tparent = parent.parent;\n\t}\n\treturn null;\n}\n\n/**\n * @template T\n * @param {Derived} derived\n * @returns {T}\n */\nexport function execute_derived(derived) {\n\tvar value;\n\tvar prev_active_effect = active_effect;\n\n\tset_active_effect(get_derived_parent_effect(derived));\n\n\tif (DEV) {\n\t\tlet prev_inspect_effects = inspect_effects;\n\t\tset_inspect_effects(new Set());\n\t\ttry {\n\t\t\tif (stack.includes(derived)) {\n\t\t\t\te.derived_references_self();\n\t\t\t}\n\n\t\t\tstack.push(derived);\n\n\t\t\tdestroy_derived_effects(derived);\n\t\t\tvalue = update_reaction(derived);\n\t\t} finally {\n\t\t\tset_active_effect(prev_active_effect);\n\t\t\tset_inspect_effects(prev_inspect_effects);\n\t\t\tstack.pop();\n\t\t}\n\t} else {\n\t\ttry {\n\t\t\tdestroy_derived_effects(derived);\n\t\t\tvalue = update_reaction(derived);\n\t\t} finally {\n\t\t\tset_active_effect(prev_active_effect);\n\t\t}\n\t}\n\n\treturn value;\n}\n\n/**\n * @param {Derived} derived\n * @returns {void}\n */\nexport function update_derived(derived) {\n\tvar value = execute_derived(derived);\n\n\tif (!derived.equals(value)) {\n\t\tderived.v = value;\n\t\tderived.wv = increment_write_version();\n\t}\n\n\t// don't mark derived clean if we're reading it inside a\n\t// cleanup function, or it will cache a stale value\n\tif (is_destroying_effect) return;\n\n\tvar status =\n\t\t(skip_reaction || (derived.f & UNOWNED) !== 0) && derived.deps !== null ? MAYBE_DIRTY : CLEAN;\n\n\tset_signal_status(derived, status);\n}\n", "/** @import { Derived, Effect, Source, Value } from '#client' */\nimport { DEV } from 'esm-env';\nimport {\n\tactive_reaction,\n\tactive_effect,\n\tuntracked_writes,\n\tget,\n\tschedule_effect,\n\tset_untracked_writes,\n\tset_signal_status,\n\tuntrack,\n\tincrement_write_version,\n\tupdate_effect,\n\treaction_sources,\n\tcheck_dirtiness,\n\tuntracking,\n\tis_destroying_effect,\n\tpush_reaction_value\n} from '../runtime.js';\nimport { equals, safe_equals } from './equality.js';\nimport {\n\tCLEAN,\n\tDERIVED,\n\tDIRTY,\n\tBRANCH_EFFECT,\n\tINSPECT_EFFECT,\n\tUNOWNED,\n\tMAYBE_DIRTY,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT\n} from '#client/constants';\nimport * as e from '../errors.js';\nimport { legacy_mode_flag, tracing_mode_flag } from '../../flags/index.js';\nimport { get_stack } from '../dev/tracing.js';\nimport { component_context, is_runes } from '../context.js';\nimport { proxy } from '../proxy.js';\nimport { execute_derived } from './deriveds.js';\n\nexport let inspect_effects = new Set();\nexport const old_values = new Map();\n\n/**\n * @param {Set<any>} v\n */\nexport function set_inspect_effects(v) {\n\tinspect_effects = v;\n}\n\n/**\n * @template V\n * @param {V} v\n * @param {Error | null} [stack]\n * @returns {Source<V>}\n */\n// TODO rename this to `state` throughout the codebase\nexport function source(v, stack) {\n\t/** @type {Value} */\n\tvar signal = {\n\t\tf: 0, // TODO ideally we could skip this altogether, but it causes type errors\n\t\tv,\n\t\treactions: null,\n\t\tequals,\n\t\trv: 0,\n\t\twv: 0\n\t};\n\n\tif (DEV && tracing_mode_flag) {\n\t\tsignal.created = stack ?? get_stack('CreatedAt');\n\t\tsignal.debug = null;\n\t}\n\n\treturn signal;\n}\n\n/**\n * @template V\n * @param {V} v\n * @param {Error | null} [stack]\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function state(v, stack) {\n\tconst s = source(v, stack);\n\n\tpush_reaction_value(s);\n\n\treturn s;\n}\n\n/**\n * @template V\n * @param {V} initial_value\n * @param {boolean} [immutable]\n * @returns {Source<V>}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function mutable_source(initial_value, immutable = false) {\n\tconst s = source(initial_value);\n\tif (!immutable) {\n\t\ts.equals = safe_equals;\n\t}\n\n\t// bind the signal to the component context, in case we need to\n\t// track updates to trigger beforeUpdate/afterUpdate callbacks\n\tif (legacy_mode_flag && component_context !== null && component_context.l !== null) {\n\t\t(component_context.l.s ??= []).push(s);\n\t}\n\n\treturn s;\n}\n\n/**\n * @template V\n * @param {Value<V>} source\n * @param {V} value\n */\nexport function mutate(source, value) {\n\tset(\n\t\tsource,\n\t\tuntrack(() => get(source))\n\t);\n\treturn value;\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n * @param {V} value\n * @param {boolean} [should_proxy]\n * @returns {V}\n */\nexport function set(source, value, should_proxy = false) {\n\tif (\n\t\tactive_reaction !== null &&\n\t\t!untracking &&\n\t\tis_runes() &&\n\t\t(active_reaction.f & (DERIVED | BLOCK_EFFECT)) !== 0 &&\n\t\t!reaction_sources?.includes(source)\n\t) {\n\t\te.state_unsafe_mutation();\n\t}\n\n\tlet new_value = should_proxy ? proxy(value) : value;\n\n\treturn internal_set(source, new_value);\n}\n\n/**\n * @template V\n * @param {Source<V>} source\n * @param {V} value\n * @returns {V}\n */\nexport function internal_set(source, value) {\n\tif (!source.equals(value)) {\n\t\tvar old_value = source.v;\n\n\t\tif (is_destroying_effect) {\n\t\t\told_values.set(source, value);\n\t\t} else {\n\t\t\told_values.set(source, old_value);\n\t\t}\n\n\t\tsource.v = value;\n\n\t\tif (DEV && tracing_mode_flag) {\n\t\t\tsource.updated = get_stack('UpdatedAt');\n\t\t\tif (active_effect != null) {\n\t\t\t\tsource.trace_need_increase = true;\n\t\t\t\tsource.trace_v ??= old_value;\n\t\t\t}\n\t\t}\n\n\t\tif ((source.f & DERIVED) !== 0) {\n\t\t\t// if we are assigning to a dirty derived we set it to clean/maybe dirty but we also eagerly execute it to track the dependencies\n\t\t\tif ((source.f & DIRTY) !== 0) {\n\t\t\t\texecute_derived(/** @type {Derived} */ (source));\n\t\t\t}\n\t\t\tset_signal_status(source, (source.f & UNOWNED) === 0 ? CLEAN : MAYBE_DIRTY);\n\t\t}\n\n\t\tsource.wv = increment_write_version();\n\n\t\tmark_reactions(source, DIRTY);\n\n\t\t// It's possible that the current reaction might not have up-to-date dependencies\n\t\t// whilst it's actively running. So in the case of ensuring it registers the reaction\n\t\t// properly for itself, we need to ensure the current effect actually gets\n\t\t// scheduled. i.e: `$effect(() => x++)`\n\t\tif (\n\t\t\tis_runes() &&\n\t\t\tactive_effect !== null &&\n\t\t\t(active_effect.f & CLEAN) !== 0 &&\n\t\t\t(active_effect.f & (BRANCH_EFFECT | ROOT_EFFECT)) === 0\n\t\t) {\n\t\t\tif (untracked_writes === null) {\n\t\t\t\tset_untracked_writes([source]);\n\t\t\t} else {\n\t\t\t\tuntracked_writes.push(source);\n\t\t\t}\n\t\t}\n\n\t\tif (DEV && inspect_effects.size > 0) {\n\t\t\tconst inspects = Array.from(inspect_effects);\n\n\t\t\tfor (const effect of inspects) {\n\t\t\t\t// Mark clean inspect-effects as maybe dirty and then check their dirtiness\n\t\t\t\t// instead of just updating the effects - this way we avoid overfiring.\n\t\t\t\tif ((effect.f & CLEAN) !== 0) {\n\t\t\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t\t\t}\n\t\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\t\tupdate_effect(effect);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tinspect_effects.clear();\n\t\t}\n\t}\n\n\treturn value;\n}\n\n/**\n * @template {number | bigint} T\n * @param {Source<T>} source\n * @param {1 | -1} [d]\n * @returns {T}\n */\nexport function update(source, d = 1) {\n\tvar value = get(source);\n\tvar result = d === 1 ? value++ : value--;\n\n\tset(source, value);\n\n\t// @ts-expect-error\n\treturn result;\n}\n\n/**\n * @template {number | bigint} T\n * @param {Source<T>} source\n * @param {1 | -1} [d]\n * @returns {T}\n */\nexport function update_pre(source, d = 1) {\n\tvar value = get(source);\n\n\t// @ts-expect-error\n\treturn set(source, d === 1 ? ++value : --value);\n}\n\n/**\n * @param {Value} signal\n * @param {number} status should be DIRTY or MAYBE_DIRTY\n * @returns {void}\n */\nfunction mark_reactions(signal, status) {\n\tvar reactions = signal.reactions;\n\tif (reactions === null) return;\n\n\tvar runes = is_runes();\n\tvar length = reactions.length;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tvar reaction = reactions[i];\n\t\tvar flags = reaction.f;\n\n\t\t// Skip any effects that are already dirty\n\t\tif ((flags & DIRTY) !== 0) continue;\n\n\t\t// In legacy mode, skip the current effect to prevent infinite loops\n\t\tif (!runes && reaction === active_effect) continue;\n\n\t\t// Inspect effects need to run immediately, so that the stack trace makes sense\n\t\tif (DEV && (flags & INSPECT_EFFECT) !== 0) {\n\t\t\tinspect_effects.add(reaction);\n\t\t\tcontinue;\n\t\t}\n\n\t\tset_signal_status(reaction, status);\n\n\t\t// If the signal a) was previously clean or b) is an unowned derived, then mark it\n\t\tif ((flags & (CLEAN | UNOWNED)) !== 0) {\n\t\t\tif ((flags & DERIVED) !== 0) {\n\t\t\t\tmark_reactions(/** @type {Derived} */ (reaction), MAYBE_DIRTY);\n\t\t\t} else {\n\t\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t\t}\n\t\t}\n\t}\n}\n", "/* This file is generated by scripts/process-messages/index.js. Do not edit! */\n\nimport { DEV } from 'esm-env';\n\nvar bold = 'font-weight: bold';\nvar normal = 'font-weight: normal';\n\n/**\n * Assignment to `%property%` property (%location%) will evaluate to the right-hand side, not the value of `%property%` following the assignment. This may result in unexpected behaviour.\n * @param {string} property\n * @param {string} location\n */\nexport function assignment_value_stale(property, location) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] assignment_value_stale\\n%cAssignment to \\`${property}\\` property (${location}) will evaluate to the right-hand side, not the value of \\`${property}\\` following the assignment. This may result in unexpected behaviour.\\nhttps://svelte.dev/e/assignment_value_stale`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/assignment_value_stale`);\n\t}\n}\n\n/**\n * `%binding%` (%location%) is binding to a non-reactive property\n * @param {string} binding\n * @param {string | undefined | null} [location]\n */\nexport function binding_property_non_reactive(binding, location) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] binding_property_non_reactive\\n%c${location ? `\\`${binding}\\` (${location}) is binding to a non-reactive property` : `\\`${binding}\\` is binding to a non-reactive property`}\\nhttps://svelte.dev/e/binding_property_non_reactive`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/binding_property_non_reactive`);\n\t}\n}\n\n/**\n * Your `console.%method%` contained `$state` proxies. Consider using `$inspect(...)` or `$state.snapshot(...)` instead\n * @param {string} method\n */\nexport function console_log_state(method) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] console_log_state\\n%cYour \\`console.${method}\\` contained \\`$state\\` proxies. Consider using \\`$inspect(...)\\` or \\`$state.snapshot(...)\\` instead\\nhttps://svelte.dev/e/console_log_state`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/console_log_state`);\n\t}\n}\n\n/**\n * %handler% should be a function. Did you mean to %suggestion%?\n * @param {string} handler\n * @param {string} suggestion\n */\nexport function event_handler_invalid(handler, suggestion) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] event_handler_invalid\\n%c${handler} should be a function. Did you mean to ${suggestion}?\\nhttps://svelte.dev/e/event_handler_invalid`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/event_handler_invalid`);\n\t}\n}\n\n/**\n * The `%attribute%` attribute on `%html%` changed its value between server and client renders. The client value, `%value%`, will be ignored in favour of the server value\n * @param {string} attribute\n * @param {string} html\n * @param {string} value\n */\nexport function hydration_attribute_changed(attribute, html, value) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] hydration_attribute_changed\\n%cThe \\`${attribute}\\` attribute on \\`${html}\\` changed its value between server and client renders. The client value, \\`${value}\\`, will be ignored in favour of the server value\\nhttps://svelte.dev/e/hydration_attribute_changed`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/hydration_attribute_changed`);\n\t}\n}\n\n/**\n * The value of an `{@html ...}` block %location% changed between server and client renders. The client value will be ignored in favour of the server value\n * @param {string | undefined | null} [location]\n */\nexport function hydration_html_changed(location) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] hydration_html_changed\\n%c${location ? `The value of an \\`{@html ...}\\` block ${location} changed between server and client renders. The client value will be ignored in favour of the server value` : 'The value of an `{@html ...}` block changed between server and client renders. The client value will be ignored in favour of the server value'}\\nhttps://svelte.dev/e/hydration_html_changed`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/hydration_html_changed`);\n\t}\n}\n\n/**\n * Hydration failed because the initial UI does not match what was rendered on the server. The error occurred near %location%\n * @param {string | undefined | null} [location]\n */\nexport function hydration_mismatch(location) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] hydration_mismatch\\n%c${location ? `Hydration failed because the initial UI does not match what was rendered on the server. The error occurred near ${location}` : 'Hydration failed because the initial UI does not match what was rendered on the server'}\\nhttps://svelte.dev/e/hydration_mismatch`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/hydration_mismatch`);\n\t}\n}\n\n/**\n * The `render` function passed to `createRawSnippet` should return HTML for a single element\n */\nexport function invalid_raw_snippet_render() {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] invalid_raw_snippet_render\\n%cThe \\`render\\` function passed to \\`createRawSnippet\\` should return HTML for a single element\\nhttps://svelte.dev/e/invalid_raw_snippet_render`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/invalid_raw_snippet_render`);\n\t}\n}\n\n/**\n * Detected a migrated `$:` reactive block in `%filename%` that both accesses and updates the same reactive value. This may cause recursive updates when converted to an `$effect`.\n * @param {string} filename\n */\nexport function legacy_recursive_reactive_block(filename) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] legacy_recursive_reactive_block\\n%cDetected a migrated \\`$:\\` reactive block in \\`${filename}\\` that both accesses and updates the same reactive value. This may cause recursive updates when converted to an \\`$effect\\`.\\nhttps://svelte.dev/e/legacy_recursive_reactive_block`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/legacy_recursive_reactive_block`);\n\t}\n}\n\n/**\n * Tried to unmount a component that was not mounted\n */\nexport function lifecycle_double_unmount() {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] lifecycle_double_unmount\\n%cTried to unmount a component that was not mounted\\nhttps://svelte.dev/e/lifecycle_double_unmount`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/lifecycle_double_unmount`);\n\t}\n}\n\n/**\n * %parent% passed property `%prop%` to %child% with `bind:`, but its parent component %owner% did not declare `%prop%` as a binding. Consider creating a binding between %owner% and %parent% (e.g. `bind:%prop%={...}` instead of `%prop%={...}`)\n * @param {string} parent\n * @param {string} prop\n * @param {string} child\n * @param {string} owner\n */\nexport function ownership_invalid_binding(parent, prop, child, owner) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] ownership_invalid_binding\\n%c${parent} passed property \\`${prop}\\` to ${child} with \\`bind:\\`, but its parent component ${owner} did not declare \\`${prop}\\` as a binding. Consider creating a binding between ${owner} and ${parent} (e.g. \\`bind:${prop}={...}\\` instead of \\`${prop}={...}\\`)\\nhttps://svelte.dev/e/ownership_invalid_binding`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/ownership_invalid_binding`);\n\t}\n}\n\n/**\n * Mutating unbound props (`%name%`, at %location%) is strongly discouraged. Consider using `bind:%prop%={...}` in %parent% (or using a callback) instead\n * @param {string} name\n * @param {string} location\n * @param {string} prop\n * @param {string} parent\n */\nexport function ownership_invalid_mutation(name, location, prop, parent) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] ownership_invalid_mutation\\n%cMutating unbound props (\\`${name}\\`, at ${location}) is strongly discouraged. Consider using \\`bind:${prop}={...}\\` in ${parent} (or using a callback) instead\\nhttps://svelte.dev/e/ownership_invalid_mutation`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/ownership_invalid_mutation`);\n\t}\n}\n\n/**\n * The `value` property of a `<select multiple>` element should be an array, but it received a non-array value. The selection will be kept as is.\n */\nexport function select_multiple_invalid_value() {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] select_multiple_invalid_value\\n%cThe \\`value\\` property of a \\`<select multiple>\\` element should be an array, but it received a non-array value. The selection will be kept as is.\\nhttps://svelte.dev/e/select_multiple_invalid_value`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/select_multiple_invalid_value`);\n\t}\n}\n\n/**\n * Reactive `$state(...)` proxies and the values they proxy have different identities. Because of this, comparisons with `%operator%` will produce unexpected results\n * @param {string} operator\n */\nexport function state_proxy_equality_mismatch(operator) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] state_proxy_equality_mismatch\\n%cReactive \\`$state(...)\\` proxies and the values they proxy have different identities. Because of this, comparisons with \\`${operator}\\` will produce unexpected results\\nhttps://svelte.dev/e/state_proxy_equality_mismatch`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/state_proxy_equality_mismatch`);\n\t}\n}\n\n/**\n * The `slide` transition does not work correctly for elements with `display: %value%`\n * @param {string} value\n */\nexport function transition_slide_display(value) {\n\tif (DEV) {\n\t\tconsole.warn(`%c[svelte] transition_slide_display\\n%cThe \\`slide\\` transition does not work correctly for elements with \\`display: ${value}\\`\\nhttps://svelte.dev/e/transition_slide_display`, bold, normal);\n\t} else {\n\t\tconsole.warn(`https://svelte.dev/e/transition_slide_display`);\n\t}\n}", "/** @import { TemplateNode } from '#client' */\n\nimport {\n\tHY<PERSON><PERSON>ION_END,\n\tHYDRATION_ERROR,\n\tHY<PERSON>ATION_START,\n\tHYDRATION_START_ELSE\n} from '../../../constants.js';\nimport * as w from '../warnings.js';\nimport { get_next_sibling } from './operations.js';\n\n/**\n * Use this variable to guard everything related to hydration code so it can be treeshaken out\n * if the user doesn't use the `hydrate` method and these code paths are therefore not needed.\n */\nexport let hydrating = false;\n\n/** @param {boolean} value */\nexport function set_hydrating(value) {\n\thydrating = value;\n}\n\n/**\n * The node that is currently being hydrated. This starts out as the first node inside the opening\n * <!--[--> comment, and updates each time a component calls `$.child(...)` or `$.sibling(...)`.\n * When entering a block (e.g. `{#if ...}`), `hydrate_node` is the block opening comment; by the\n * time we leave the block it is the closing comment, which serves as the block's anchor.\n * @type {TemplateNode}\n */\nexport let hydrate_node;\n\n/** @param {TemplateNode} node */\nexport function set_hydrate_node(node) {\n\tif (node === null) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\treturn (hydrate_node = node);\n}\n\nexport function hydrate_next() {\n\treturn set_hydrate_node(/** @type {TemplateNode} */ (get_next_sibling(hydrate_node)));\n}\n\n/** @param {TemplateNode} node */\nexport function reset(node) {\n\tif (!hydrating) return;\n\n\t// If the node has remaining siblings, something has gone wrong\n\tif (get_next_sibling(hydrate_node) !== null) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\thydrate_node = node;\n}\n\n/**\n * @param {HTMLTemplateElement} template\n */\nexport function hydrate_template(template) {\n\tif (hydrating) {\n\t\t// @ts-expect-error TemplateNode doesn't include DocumentFragment, but it's actually fine\n\t\thydrate_node = template.content;\n\t}\n}\n\nexport function next(count = 1) {\n\tif (hydrating) {\n\t\tvar i = count;\n\t\tvar node = hydrate_node;\n\n\t\twhile (i--) {\n\t\t\tnode = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\t}\n\n\t\thydrate_node = node;\n\t}\n}\n\n/**\n * Removes all nodes starting at `hydrate_node` up until the next hydration end comment\n */\nexport function remove_nodes() {\n\tvar depth = 0;\n\tvar node = hydrate_node;\n\n\twhile (true) {\n\t\tif (node.nodeType === 8) {\n\t\t\tvar data = /** @type {Comment} */ (node).data;\n\n\t\t\tif (data === HYDRATION_END) {\n\t\t\t\tif (depth === 0) return node;\n\t\t\t\tdepth -= 1;\n\t\t\t} else if (data === HYDRATION_START || data === HYDRATION_START_ELSE) {\n\t\t\t\tdepth += 1;\n\t\t\t}\n\t\t}\n\n\t\tvar next = /** @type {TemplateNode} */ (get_next_sibling(node));\n\t\tnode.remove();\n\t\tnode = next;\n\t}\n}\n\n/**\n *\n * @param {TemplateNode} node\n */\nexport function read_hydration_instruction(node) {\n\tif (!node || node.nodeType !== 8) {\n\t\tw.hydration_mismatch();\n\t\tthrow HYDRATION_ERROR;\n\t}\n\n\treturn /** @type {Comment} */ (node).data;\n}\n", "import * as w from '../warnings.js';\nimport { get_proxied_value } from '../proxy.js';\n\nexport function init_array_prototype_warnings() {\n\tconst array_prototype = Array.prototype;\n\t// The REPL ends up here over and over, and this prevents it from adding more and more patches\n\t// of the same kind to the prototype, which would slow down everything over time.\n\t// @ts-expect-error\n\tconst cleanup = Array.__svelte_cleanup;\n\tif (cleanup) {\n\t\tcleanup();\n\t}\n\n\tconst { indexOf, lastIndexOf, includes } = array_prototype;\n\n\tarray_prototype.indexOf = function (item, from_index) {\n\t\tconst index = indexOf.call(this, item, from_index);\n\n\t\tif (index === -1) {\n\t\t\tfor (let i = from_index ?? 0; i < this.length; i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.indexOf(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn index;\n\t};\n\n\tarray_prototype.lastIndexOf = function (item, from_index) {\n\t\t// we need to specify this.length - 1 because it's probably using something like\n\t\t// `arguments` inside so passing undefined is different from not passing anything\n\t\tconst index = lastIndexOf.call(this, item, from_index ?? this.length - 1);\n\n\t\tif (index === -1) {\n\t\t\tfor (let i = 0; i <= (from_index ?? this.length - 1); i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.lastIndexOf(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn index;\n\t};\n\n\tarray_prototype.includes = function (item, from_index) {\n\t\tconst has = includes.call(this, item, from_index);\n\n\t\tif (!has) {\n\t\t\tfor (let i = 0; i < this.length; i += 1) {\n\t\t\t\tif (get_proxied_value(this[i]) === item) {\n\t\t\t\t\tw.state_proxy_equality_mismatch('array.includes(...)');\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn has;\n\t};\n\n\t// @ts-expect-error\n\tArray.__svelte_cleanup = () => {\n\t\tarray_prototype.indexOf = indexOf;\n\t\tarray_prototype.lastIndexOf = lastIndexOf;\n\t\tarray_prototype.includes = includes;\n\t};\n}\n\n/**\n * @param {any} a\n * @param {any} b\n * @param {boolean} equal\n * @returns {boolean}\n */\nexport function strict_equals(a, b, equal = true) {\n\t// try-catch needed because this tries to read properties of `a` and `b`,\n\t// which could be disallowed for example in a secure context\n\ttry {\n\t\tif ((a === b) !== (get_proxied_value(a) === get_proxied_value(b))) {\n\t\t\tw.state_proxy_equality_mismatch(equal ? '===' : '!==');\n\t\t}\n\t} catch {}\n\n\treturn (a === b) === equal;\n}\n\n/**\n * @param {any} a\n * @param {any} b\n * @param {boolean} equal\n * @returns {boolean}\n */\nexport function equals(a, b, equal = true) {\n\tif ((a == b) !== (get_proxied_value(a) == get_proxied_value(b))) {\n\t\tw.state_proxy_equality_mismatch(equal ? '==' : '!=');\n\t}\n\n\treturn (a == b) === equal;\n}\n", "/** @import { TemplateNode } from '#client' */\nimport { hydrate_node, hydrating, set_hydrate_node } from './hydration.js';\nimport { DEV } from 'esm-env';\nimport { init_array_prototype_warnings } from '../dev/equality.js';\nimport { get_descriptor, is_extensible } from '../../shared/utils.js';\n\n// export these for reference in the compiled code, making global name deduplication unnecessary\n/** @type {Window} */\nexport var $window;\n\n/** @type {Document} */\nexport var $document;\n\n/** @type {boolean} */\nexport var is_firefox;\n\n/** @type {() => Node | null} */\nvar first_child_getter;\n/** @type {() => Node | null} */\nvar next_sibling_getter;\n\n/**\n * Initialize these lazily to avoid issues when using the runtime in a server context\n * where these globals are not available while avoiding a separate server entry point\n */\nexport function init_operations() {\n\tif ($window !== undefined) {\n\t\treturn;\n\t}\n\n\t$window = window;\n\t$document = document;\n\tis_firefox = /Firefox/.test(navigator.userAgent);\n\n\tvar element_prototype = Element.prototype;\n\tvar node_prototype = Node.prototype;\n\tvar text_prototype = Text.prototype;\n\n\t// @ts-ignore\n\tfirst_child_getter = get_descriptor(node_prototype, 'firstChild').get;\n\t// @ts-ignore\n\tnext_sibling_getter = get_descriptor(node_prototype, 'nextSibling').get;\n\n\tif (is_extensible(element_prototype)) {\n\t\t// the following assignments improve perf of lookups on DOM nodes\n\t\t// @ts-expect-error\n\t\telement_prototype.__click = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__className = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__attributes = null;\n\t\t// @ts-expect-error\n\t\telement_prototype.__style = undefined;\n\t\t// @ts-expect-error\n\t\telement_prototype.__e = undefined;\n\t}\n\n\tif (is_extensible(text_prototype)) {\n\t\t// @ts-expect-error\n\t\ttext_prototype.__t = undefined;\n\t}\n\n\tif (DEV) {\n\t\t// @ts-expect-error\n\t\telement_prototype.__svelte_meta = null;\n\n\t\tinit_array_prototype_warnings();\n\t}\n}\n\n/**\n * @param {string} value\n * @returns {Text}\n */\nexport function create_text(value = '') {\n\treturn document.createTextNode(value);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {Node | null}\n */\n/*@__NO_SIDE_EFFECTS__*/\nexport function get_first_child(node) {\n\treturn first_child_getter.call(node);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {Node | null}\n */\n/*@__NO_SIDE_EFFECTS__*/\nexport function get_next_sibling(node) {\n\treturn next_sibling_getter.call(node);\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @template {Node} N\n * @param {N} node\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function child(node, is_text) {\n\tif (!hydrating) {\n\t\treturn get_first_child(node);\n\t}\n\n\tvar child = /** @type {TemplateNode} */ (get_first_child(hydrate_node));\n\n\t// Child can be null if we have an element with a single child, like `<p>{text}</p>`, where `text` is empty\n\tif (child === null) {\n\t\tchild = hydrate_node.appendChild(create_text());\n\t} else if (is_text && child.nodeType !== 3) {\n\t\tvar text = create_text();\n\t\tchild?.before(text);\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\tset_hydrate_node(child);\n\treturn child;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {DocumentFragment | TemplateNode[]} fragment\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function first_child(fragment, is_text) {\n\tif (!hydrating) {\n\t\t// when not hydrating, `fragment` is a `DocumentFragment` (the result of calling `open_frag`)\n\t\tvar first = /** @type {DocumentFragment} */ (get_first_child(/** @type {Node} */ (fragment)));\n\n\t\t// TODO prevent user comments with the empty string when preserveComments is true\n\t\tif (first instanceof Comment && first.data === '') return get_next_sibling(first);\n\n\t\treturn first;\n\t}\n\n\t// if an {expression} is empty during SSR, there might be no\n\t// text node to hydrate — we must therefore create one\n\tif (is_text && hydrate_node?.nodeType !== 3) {\n\t\tvar text = create_text();\n\n\t\thydrate_node?.before(text);\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\treturn hydrate_node;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {TemplateNode} node\n * @param {number} count\n * @param {boolean} is_text\n * @returns {Node | null}\n */\nexport function sibling(node, count = 1, is_text = false) {\n\tlet next_sibling = hydrating ? hydrate_node : node;\n\tvar last_sibling;\n\n\twhile (count--) {\n\t\tlast_sibling = next_sibling;\n\t\tnext_sibling = /** @type {TemplateNode} */ (get_next_sibling(next_sibling));\n\t}\n\n\tif (!hydrating) {\n\t\treturn next_sibling;\n\t}\n\n\tvar type = next_sibling?.nodeType;\n\n\t// if a sibling {expression} is empty during SSR, there might be no\n\t// text node to hydrate — we must therefore create one\n\tif (is_text && type !== 3) {\n\t\tvar text = create_text();\n\t\t// If the next sibling is `null` and we're handling text then it's because\n\t\t// the SSR content was empty for the text, so we need to generate a new text\n\t\t// node and insert it after the last sibling\n\t\tif (next_sibling === null) {\n\t\t\tlast_sibling?.after(text);\n\t\t} else {\n\t\t\tnext_sibling.before(text);\n\t\t}\n\t\tset_hydrate_node(text);\n\t\treturn text;\n\t}\n\n\tset_hydrate_node(next_sibling);\n\treturn /** @type {TemplateNode} */ (next_sibling);\n}\n\n/**\n * @template {Node} N\n * @param {N} node\n * @returns {void}\n */\nexport function clear_text_content(node) {\n\tnode.textContent = '';\n}\n\n/**\n *\n * @param {string} tag\n * @param {string} [namespace]\n * @param {string} [is]\n * @returns\n */\nexport function create_element(tag, namespace, is) {\n\tlet options = is ? { is } : undefined;\n\tif (namespace) {\n\t\treturn document.createElementNS(namespace, tag, options);\n\t}\n\treturn document.createElement(tag, options);\n}\n\nexport function create_fragment() {\n\treturn document.createDocumentFragment();\n}\n\n/**\n * @param {string} data\n * @returns\n */\nexport function create_comment(data = '') {\n\treturn document.createComment(data);\n}\n\n/**\n * @param {Element} element\n * @param {string} key\n * @param {string} value\n * @returns\n */\nexport function set_attribute(element, key, value = '') {\n\tif (key.startsWith('xlink:')) {\n\t\telement.setAttributeNS('http://www.w3.org/1999/xlink', key, value);\n\t\treturn;\n\t}\n\treturn element.setAttribute(key, value);\n}\n", "/** @import { ComponentContext, ComponentContextLegacy, Derived, Effect, TemplateNode, TransitionManager } from '#client' */\nimport {\n\tcheck_dirtiness,\n\tactive_effect,\n\tactive_reaction,\n\tupdate_effect,\n\tget,\n\tis_destroying_effect,\n\tremove_reactions,\n\tschedule_effect,\n\tset_active_reaction,\n\tset_is_destroying_effect,\n\tset_signal_status,\n\tuntrack,\n\tuntracking\n} from '../runtime.js';\nimport {\n\tDIRTY,\n\tBRANCH_EFFECT,\n\tRENDER_EFFECT,\n\tEFFECT,\n\tDESTROYED,\n\tINERT,\n\tEFFECT_RAN,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tEFFECT_TRANSPARENT,\n\tDERIVED,\n\tUNOWNED,\n\tCLEAN,\n\tINSPECT_EFFECT,\n\tHEAD_EFFECT,\n\tMAYBE_DIRTY,\n\tEFFECT_HAS_DERIVED,\n\tBOUNDARY_EFFECT\n} from '#client/constants';\nimport { set } from './sources.js';\nimport * as e from '../errors.js';\nimport { DEV } from 'esm-env';\nimport { define_property } from '../../shared/utils.js';\nimport { get_next_sibling } from '../dom/operations.js';\nimport { derived } from './deriveds.js';\nimport { component_context, dev_current_component_function } from '../context.js';\n\n/**\n * @param {'$effect' | '$effect.pre' | '$inspect'} rune\n */\nexport function validate_effect(rune) {\n\tif (active_effect === null && active_reaction === null) {\n\t\te.effect_orphan(rune);\n\t}\n\n\tif (active_reaction !== null && (active_reaction.f & UNOWNED) !== 0 && active_effect === null) {\n\t\te.effect_in_unowned_derived();\n\t}\n\n\tif (is_destroying_effect) {\n\t\te.effect_in_teardown(rune);\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {Effect} parent_effect\n */\nfunction push_effect(effect, parent_effect) {\n\tvar parent_last = parent_effect.last;\n\tif (parent_last === null) {\n\t\tparent_effect.last = parent_effect.first = effect;\n\t} else {\n\t\tparent_last.next = effect;\n\t\teffect.prev = parent_last;\n\t\tparent_effect.last = effect;\n\t}\n}\n\n/**\n * @param {number} type\n * @param {null | (() => void | (() => void))} fn\n * @param {boolean} sync\n * @param {boolean} push\n * @returns {Effect}\n */\nfunction create_effect(type, fn, sync, push = true) {\n\tvar parent = active_effect;\n\n\tif (DEV) {\n\t\t// Ensure the parent is never an inspect effect\n\t\twhile (parent !== null && (parent.f & INSPECT_EFFECT) !== 0) {\n\t\t\tparent = parent.parent;\n\t\t}\n\t}\n\n\t/** @type {Effect} */\n\tvar effect = {\n\t\tctx: component_context,\n\t\tdeps: null,\n\t\tnodes_start: null,\n\t\tnodes_end: null,\n\t\tf: type | DIRTY,\n\t\tfirst: null,\n\t\tfn,\n\t\tlast: null,\n\t\tnext: null,\n\t\tparent,\n\t\tprev: null,\n\t\tteardown: null,\n\t\ttransitions: null,\n\t\twv: 0\n\t};\n\n\tif (DEV) {\n\t\teffect.component_function = dev_current_component_function;\n\t}\n\n\tif (sync) {\n\t\ttry {\n\t\t\tupdate_effect(effect);\n\t\t\teffect.f |= EFFECT_RAN;\n\t\t} catch (e) {\n\t\t\tdestroy_effect(effect);\n\t\t\tthrow e;\n\t\t}\n\t} else if (fn !== null) {\n\t\tschedule_effect(effect);\n\t}\n\n\t// if an effect has no dependencies, no DOM and no teardown function,\n\t// don't bother adding it to the effect tree\n\tvar inert =\n\t\tsync &&\n\t\teffect.deps === null &&\n\t\teffect.first === null &&\n\t\teffect.nodes_start === null &&\n\t\teffect.teardown === null &&\n\t\t(effect.f & (EFFECT_HAS_DERIVED | BOUNDARY_EFFECT)) === 0;\n\n\tif (!inert && push) {\n\t\tif (parent !== null) {\n\t\t\tpush_effect(effect, parent);\n\t\t}\n\n\t\t// if we're in a derived, add the effect there too\n\t\tif (active_reaction !== null && (active_reaction.f & DERIVED) !== 0) {\n\t\t\tvar derived = /** @type {Derived} */ (active_reaction);\n\t\t\t(derived.effects ??= []).push(effect);\n\t\t}\n\t}\n\n\treturn effect;\n}\n\n/**\n * Internal representation of `$effect.tracking()`\n * @returns {boolean}\n */\nexport function effect_tracking() {\n\treturn active_reaction !== null && !untracking;\n}\n\n/**\n * @param {() => void} fn\n */\nexport function teardown(fn) {\n\tconst effect = create_effect(RENDER_EFFECT, null, false);\n\tset_signal_status(effect, CLEAN);\n\teffect.teardown = fn;\n\treturn effect;\n}\n\n/**\n * Internal representation of `$effect(...)`\n * @param {() => void | (() => void)} fn\n */\nexport function user_effect(fn) {\n\tvalidate_effect('$effect');\n\n\t// Non-nested `$effect(...)` in a component should be deferred\n\t// until the component is mounted\n\tvar defer =\n\t\tactive_effect !== null &&\n\t\t(active_effect.f & BRANCH_EFFECT) !== 0 &&\n\t\tcomponent_context !== null &&\n\t\t!component_context.m;\n\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '$effect'\n\t\t});\n\t}\n\n\tif (defer) {\n\t\tvar context = /** @type {ComponentContext} */ (component_context);\n\t\t(context.e ??= []).push({\n\t\t\tfn,\n\t\t\teffect: active_effect,\n\t\t\treaction: active_reaction\n\t\t});\n\t} else {\n\t\tvar signal = effect(fn);\n\t\treturn signal;\n\t}\n}\n\n/**\n * Internal representation of `$effect.pre(...)`\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function user_pre_effect(fn) {\n\tvalidate_effect('$effect.pre');\n\tif (DEV) {\n\t\tdefine_property(fn, 'name', {\n\t\t\tvalue: '$effect.pre'\n\t\t});\n\t}\n\treturn render_effect(fn);\n}\n\n/** @param {() => void | (() => void)} fn */\nexport function inspect_effect(fn) {\n\treturn create_effect(INSPECT_EFFECT, fn, true);\n}\n\n/**\n * Internal representation of `$effect.root(...)`\n * @param {() => void | (() => void)} fn\n * @returns {() => void}\n */\nexport function effect_root(fn) {\n\tconst effect = create_effect(ROOT_EFFECT, fn, true);\n\n\treturn () => {\n\t\tdestroy_effect(effect);\n\t};\n}\n\n/**\n * An effect root whose children can transition out\n * @param {() => void} fn\n * @returns {(options?: { outro?: boolean }) => Promise<void>}\n */\nexport function component_root(fn) {\n\tconst effect = create_effect(ROOT_EFFECT, fn, true);\n\n\treturn (options = {}) => {\n\t\treturn new Promise((fulfil) => {\n\t\t\tif (options.outro) {\n\t\t\t\tpause_effect(effect, () => {\n\t\t\t\t\tdestroy_effect(effect);\n\t\t\t\t\tfulfil(undefined);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tdestroy_effect(effect);\n\t\t\t\tfulfil(undefined);\n\t\t\t}\n\t\t});\n\t};\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function effect(fn) {\n\treturn create_effect(EFFECT, fn, false);\n}\n\n/**\n * Internal representation of `$: ..`\n * @param {() => any} deps\n * @param {() => void | (() => void)} fn\n */\nexport function legacy_pre_effect(deps, fn) {\n\tvar context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\t/** @type {{ effect: null | Effect, ran: boolean }} */\n\tvar token = { effect: null, ran: false };\n\tcontext.l.r1.push(token);\n\n\ttoken.effect = render_effect(() => {\n\t\tdeps();\n\n\t\t// If this legacy pre effect has already run before the end of the reset, then\n\t\t// bail out to emulate the same behavior.\n\t\tif (token.ran) return;\n\n\t\ttoken.ran = true;\n\t\tset(context.l.r2, true);\n\t\tuntrack(fn);\n\t});\n}\n\nexport function legacy_pre_effect_reset() {\n\tvar context = /** @type {ComponentContextLegacy} */ (component_context);\n\n\trender_effect(() => {\n\t\tif (!get(context.l.r2)) return;\n\n\t\t// Run dirty `$:` statements\n\t\tfor (var token of context.l.r1) {\n\t\t\tvar effect = token.effect;\n\n\t\t\t// If the effect is CLEAN, then make it MAYBE_DIRTY. This ensures we traverse through\n\t\t\t// the effects dependencies and correctly ensure each dependency is up-to-date.\n\t\t\tif ((effect.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t\t}\n\n\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\tupdate_effect(effect);\n\t\t\t}\n\n\t\t\ttoken.ran = false;\n\t\t}\n\n\t\tcontext.l.r2.v = false; // set directly to avoid rerunning this effect\n\t});\n}\n\n/**\n * @param {() => void | (() => void)} fn\n * @returns {Effect}\n */\nexport function render_effect(fn) {\n\treturn create_effect(RENDER_EFFECT, fn, true);\n}\n\n/**\n * @param {(...expressions: any) => void | (() => void)} fn\n * @param {Array<() => any>} thunks\n * @returns {Effect}\n */\nexport function template_effect(fn, thunks = [], d = derived) {\n\tconst deriveds = thunks.map(d);\n\tconst effect = () => fn(...deriveds.map(get));\n\n\tif (DEV) {\n\t\tdefine_property(effect, 'name', {\n\t\t\tvalue: '{expression}'\n\t\t});\n\t}\n\n\treturn block(effect);\n}\n\n/**\n * @param {(() => void)} fn\n * @param {number} flags\n */\nexport function block(fn, flags = 0) {\n\treturn create_effect(RENDER_EFFECT | BLOCK_EFFECT | flags, fn, true);\n}\n\n/**\n * @param {(() => void)} fn\n * @param {boolean} [push]\n */\nexport function branch(fn, push = true) {\n\treturn create_effect(RENDER_EFFECT | BRANCH_EFFECT, fn, true, push);\n}\n\n/**\n * @param {Effect} effect\n */\nexport function execute_effect_teardown(effect) {\n\tvar teardown = effect.teardown;\n\tif (teardown !== null) {\n\t\tconst previously_destroying_effect = is_destroying_effect;\n\t\tconst previous_reaction = active_reaction;\n\t\tset_is_destroying_effect(true);\n\t\tset_active_reaction(null);\n\t\ttry {\n\t\t\tteardown.call(null);\n\t\t} finally {\n\t\t\tset_is_destroying_effect(previously_destroying_effect);\n\t\t\tset_active_reaction(previous_reaction);\n\t\t}\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @param {boolean} remove_dom\n * @returns {void}\n */\nexport function destroy_effect_children(signal, remove_dom = false) {\n\tvar effect = signal.first;\n\tsignal.first = signal.last = null;\n\n\twhile (effect !== null) {\n\t\tvar next = effect.next;\n\n\t\tif ((effect.f & ROOT_EFFECT) !== 0) {\n\t\t\t// this is now an independent root\n\t\t\teffect.parent = null;\n\t\t} else {\n\t\t\tdestroy_effect(effect, remove_dom);\n\t\t}\n\n\t\teffect = next;\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function destroy_block_effect_children(signal) {\n\tvar effect = signal.first;\n\n\twhile (effect !== null) {\n\t\tvar next = effect.next;\n\t\tif ((effect.f & BRANCH_EFFECT) === 0) {\n\t\t\tdestroy_effect(effect);\n\t\t}\n\t\teffect = next;\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {boolean} [remove_dom]\n * @returns {void}\n */\nexport function destroy_effect(effect, remove_dom = true) {\n\tvar removed = false;\n\n\tif ((remove_dom || (effect.f & HEAD_EFFECT) !== 0) && effect.nodes_start !== null) {\n\t\tremove_effect_dom(effect.nodes_start, /** @type {TemplateNode} */ (effect.nodes_end));\n\t\tremoved = true;\n\t}\n\n\tdestroy_effect_children(effect, remove_dom && !removed);\n\tremove_reactions(effect, 0);\n\tset_signal_status(effect, DESTROYED);\n\n\tvar transitions = effect.transitions;\n\n\tif (transitions !== null) {\n\t\tfor (const transition of transitions) {\n\t\t\ttransition.stop();\n\t\t}\n\t}\n\n\texecute_effect_teardown(effect);\n\n\tvar parent = effect.parent;\n\n\t// If the parent doesn't have any children, then skip this work altogether\n\tif (parent !== null && parent.first !== null) {\n\t\tunlink_effect(effect);\n\t}\n\n\tif (DEV) {\n\t\teffect.component_function = null;\n\t}\n\n\t// `first` and `child` are nulled out in destroy_effect_children\n\t// we don't null out `parent` so that error propagation can work correctly\n\teffect.next =\n\t\teffect.prev =\n\t\teffect.teardown =\n\t\teffect.ctx =\n\t\teffect.deps =\n\t\teffect.fn =\n\t\teffect.nodes_start =\n\t\teffect.nodes_end =\n\t\t\tnull;\n}\n\n/**\n *\n * @param {TemplateNode | null} node\n * @param {TemplateNode} end\n */\nexport function remove_effect_dom(node, end) {\n\twhile (node !== null) {\n\t\t/** @type {TemplateNode | null} */\n\t\tvar next = node === end ? null : /** @type {TemplateNode} */ (get_next_sibling(node));\n\n\t\tnode.remove();\n\t\tnode = next;\n\t}\n}\n\n/**\n * Detach an effect from the effect tree, freeing up memory and\n * reducing the amount of work that happens on subsequent traversals\n * @param {Effect} effect\n */\nexport function unlink_effect(effect) {\n\tvar parent = effect.parent;\n\tvar prev = effect.prev;\n\tvar next = effect.next;\n\n\tif (prev !== null) prev.next = next;\n\tif (next !== null) next.prev = prev;\n\n\tif (parent !== null) {\n\t\tif (parent.first === effect) parent.first = next;\n\t\tif (parent.last === effect) parent.last = prev;\n\t}\n}\n\n/**\n * When a block effect is removed, we don't immediately destroy it or yank it\n * out of the DOM, because it might have transitions. Instead, we 'pause' it.\n * It stays around (in memory, and in the DOM) until outro transitions have\n * completed, and if the state change is reversed then we _resume_ it.\n * A paused effect does not update, and the DOM subtree becomes inert.\n * @param {Effect} effect\n * @param {() => void} [callback]\n */\nexport function pause_effect(effect, callback) {\n\t/** @type {TransitionManager[]} */\n\tvar transitions = [];\n\n\tpause_children(effect, transitions, true);\n\n\trun_out_transitions(transitions, () => {\n\t\tdestroy_effect(effect);\n\t\tif (callback) callback();\n\t});\n}\n\n/**\n * @param {TransitionManager[]} transitions\n * @param {() => void} fn\n */\nexport function run_out_transitions(transitions, fn) {\n\tvar remaining = transitions.length;\n\tif (remaining > 0) {\n\t\tvar check = () => --remaining || fn();\n\t\tfor (var transition of transitions) {\n\t\t\ttransition.out(check);\n\t\t}\n\t} else {\n\t\tfn();\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @param {TransitionManager[]} transitions\n * @param {boolean} local\n */\nexport function pause_children(effect, transitions, local) {\n\tif ((effect.f & INERT) !== 0) return;\n\teffect.f ^= INERT;\n\n\tif (effect.transitions !== null) {\n\t\tfor (const transition of effect.transitions) {\n\t\t\tif (transition.is_global || local) {\n\t\t\t\ttransitions.push(transition);\n\t\t\t}\n\t\t}\n\t}\n\n\tvar child = effect.first;\n\n\twhile (child !== null) {\n\t\tvar sibling = child.next;\n\t\tvar transparent = (child.f & EFFECT_TRANSPARENT) !== 0 || (child.f & BRANCH_EFFECT) !== 0;\n\t\t// TODO we don't need to call pause_children recursively with a linked list in place\n\t\t// it's slightly more involved though as we have to account for `transparent` changing\n\t\t// through the tree.\n\t\tpause_children(child, transitions, transparent ? local : false);\n\t\tchild = sibling;\n\t}\n}\n\n/**\n * The opposite of `pause_effect`. We call this if (for example)\n * `x` becomes falsy then truthy: `{#if x}...{/if}`\n * @param {Effect} effect\n */\nexport function resume_effect(effect) {\n\tresume_children(effect, true);\n}\n\n/**\n * @param {Effect} effect\n * @param {boolean} local\n */\nfunction resume_children(effect, local) {\n\tif ((effect.f & INERT) === 0) return;\n\teffect.f ^= INERT;\n\n\t// Ensure the effect is marked as clean again so that any dirty child\n\t// effects can schedule themselves for execution\n\tif ((effect.f & CLEAN) === 0) {\n\t\teffect.f ^= CLEAN;\n\t}\n\n\t// If a dependency of this effect changed while it was paused,\n\t// schedule the effect to update\n\tif (check_dirtiness(effect)) {\n\t\tset_signal_status(effect, DIRTY);\n\t\tschedule_effect(effect);\n\t}\n\n\tvar child = effect.first;\n\n\twhile (child !== null) {\n\t\tvar sibling = child.next;\n\t\tvar transparent = (child.f & EFFECT_TRANSPARENT) !== 0 || (child.f & BRANCH_EFFECT) !== 0;\n\t\t// TODO we don't need to call resume_children recursively with a linked list in place\n\t\t// it's slightly more involved though as we have to account for `transparent` changing\n\t\t// through the tree.\n\t\tresume_children(child, transparent ? local : false);\n\t\tchild = sibling;\n\t}\n\n\tif (effect.transitions !== null) {\n\t\tfor (const transition of effect.transitions) {\n\t\t\tif (transition.is_global || local) {\n\t\t\t\ttransition.in();\n\t\t\t}\n\t\t}\n\t}\n}\n", "import { run_all } from '../../shared/utils.js';\n\n// Fallback for when requestIdleCallback is not available\nconst request_idle_callback =\n\ttypeof requestIdleCallback === 'undefined'\n\t\t? (/** @type {() => void} */ cb) => setTimeout(cb, 1)\n\t\t: requestIdleCallback;\n\n/** @type {Array<() => void>} */\nlet micro_tasks = [];\n\n/** @type {Array<() => void>} */\nlet idle_tasks = [];\n\nfunction run_micro_tasks() {\n\tvar tasks = micro_tasks;\n\tmicro_tasks = [];\n\trun_all(tasks);\n}\n\nfunction run_idle_tasks() {\n\tvar tasks = idle_tasks;\n\tidle_tasks = [];\n\trun_all(tasks);\n}\n\n/**\n * @param {() => void} fn\n */\nexport function queue_micro_task(fn) {\n\tif (micro_tasks.length === 0) {\n\t\tqueueMicrotask(run_micro_tasks);\n\t}\n\n\tmicro_tasks.push(fn);\n}\n\n/**\n * @param {() => void} fn\n */\nexport function queue_idle_task(fn) {\n\tif (idle_tasks.length === 0) {\n\t\trequest_idle_callback(run_idle_tasks);\n\t}\n\n\tidle_tasks.push(fn);\n}\n\n/**\n * Synchronously run any queued tasks.\n */\nexport function flush_tasks() {\n\tif (micro_tasks.length > 0) {\n\t\trun_micro_tasks();\n\t}\n\n\tif (idle_tasks.length > 0) {\n\t\trun_idle_tasks();\n\t}\n}\n", "/** @import { ComponentContext, Derived, Effect, Reaction, Signal, Source, Value } from '#client' */\nimport { DEV } from 'esm-env';\nimport { define_property, get_descriptors, get_prototype_of, index_of } from '../shared/utils.js';\nimport {\n\tdestroy_block_effect_children,\n\tdestroy_effect_children,\n\texecute_effect_teardown,\n\tunlink_effect\n} from './reactivity/effects.js';\nimport {\n\tEFFECT,\n\tDIRTY,\n\tMAYBE_DIRTY,\n\tCLEAN,\n\tDERIVED,\n\tUNOWNED,\n\tDESTROYED,\n\tINERT,\n\tBRANCH_EFFECT,\n\tSTATE_SYMBOL,\n\tBLOCK_EFFECT,\n\tROOT_EFFECT,\n\tLEGACY_DERIVED_PROP,\n\tDISCONNECTED,\n\tBOUNDARY_EFFECT,\n\tEFFECT_IS_UPDATING\n} from './constants.js';\nimport { flush_tasks } from './dom/task.js';\nimport { internal_set, old_values } from './reactivity/sources.js';\nimport { destroy_derived_effects, update_derived } from './reactivity/deriveds.js';\nimport * as e from './errors.js';\nimport { FILENAME } from '../../constants.js';\nimport { tracing_mode_flag } from '../flags/index.js';\nimport { tracing_expressions, get_stack } from './dev/tracing.js';\nimport {\n\tcomponent_context,\n\tdev_current_component_function,\n\tis_runes,\n\tset_component_context,\n\tset_dev_current_component_function\n} from './context.js';\nimport { is_firefox } from './dom/operations.js';\n\n// Used for DEV time error handling\n/** @param {WeakSet<Error>} value */\nconst handled_errors = new WeakSet();\nlet is_throwing_error = false;\n\nlet is_flushing = false;\n\n/** @type {Effect | null} */\nlet last_scheduled_effect = null;\n\nlet is_updating_effect = false;\n\nexport let is_destroying_effect = false;\n\n/** @param {boolean} value */\nexport function set_is_destroying_effect(value) {\n\tis_destroying_effect = value;\n}\n\n// Handle effect queues\n\n/** @type {Effect[]} */\nlet queued_root_effects = [];\n\n/** @type {Effect[]} Stack of effects, dev only */\nlet dev_effect_stack = [];\n// Handle signal reactivity tree dependencies and reactions\n\n/** @type {null | Reaction} */\nexport let active_reaction = null;\n\nexport let untracking = false;\n\n/** @param {null | Reaction} reaction */\nexport function set_active_reaction(reaction) {\n\tactive_reaction = reaction;\n}\n\n/** @type {null | Effect} */\nexport let active_effect = null;\n\n/** @param {null | Effect} effect */\nexport function set_active_effect(effect) {\n\tactive_effect = effect;\n}\n\n/**\n * When sources are created within a reaction, reading and writing\n * them should not cause a re-run\n * @type {null | Source[]}\n */\nexport let reaction_sources = null;\n\n/** @param {Value} value */\nexport function push_reaction_value(value) {\n\tif (active_reaction !== null && active_reaction.f & EFFECT_IS_UPDATING) {\n\t\tif (reaction_sources === null) {\n\t\t\treaction_sources = [value];\n\t\t} else {\n\t\t\treaction_sources.push(value);\n\t\t}\n\t}\n}\n\n/**\n * The dependencies of the reaction that is currently being executed. In many cases,\n * the dependencies are unchanged between runs, and so this will be `null` unless\n * and until a new dependency is accessed — we track this via `skipped_deps`\n * @type {null | Value[]}\n */\nlet new_deps = null;\n\nlet skipped_deps = 0;\n\n/**\n * Tracks writes that the effect it's executed in doesn't listen to yet,\n * so that the dependency can be added to the effect later on if it then reads it\n * @type {null | Source[]}\n */\nexport let untracked_writes = null;\n\n/** @param {null | Source[]} value */\nexport function set_untracked_writes(value) {\n\tuntracked_writes = value;\n}\n\n/**\n * @type {number} Used by sources and deriveds for handling updates.\n * Version starts from 1 so that unowned deriveds differentiate between a created effect and a run one for tracing\n **/\nlet write_version = 1;\n\n/** @type {number} Used to version each read of a source of derived to avoid duplicating depedencies inside a reaction */\nlet read_version = 0;\n\n// If we are working with a get() chain that has no active container,\n// to prevent memory leaks, we skip adding the reaction.\nexport let skip_reaction = false;\n// Handle collecting all signals which are read during a specific time frame\n/** @type {Set<Value> | null} */\nexport let captured_signals = null;\n\n/** @param {Set<Value> | null} value */\nexport function set_captured_signals(value) {\n\tcaptured_signals = value;\n}\n\nexport function increment_write_version() {\n\treturn ++write_version;\n}\n\n/**\n * Determines whether a derived or effect is dirty.\n * If it is MAYBE_DIRTY, will set the status to CLEAN\n * @param {Reaction} reaction\n * @returns {boolean}\n */\nexport function check_dirtiness(reaction) {\n\tvar flags = reaction.f;\n\n\tif ((flags & DIRTY) !== 0) {\n\t\treturn true;\n\t}\n\n\tif ((flags & MAYBE_DIRTY) !== 0) {\n\t\tvar dependencies = reaction.deps;\n\t\tvar is_unowned = (flags & UNOWNED) !== 0;\n\n\t\tif (dependencies !== null) {\n\t\t\tvar i;\n\t\t\tvar dependency;\n\t\t\tvar is_disconnected = (flags & DISCONNECTED) !== 0;\n\t\t\tvar is_unowned_connected = is_unowned && active_effect !== null && !skip_reaction;\n\t\t\tvar length = dependencies.length;\n\n\t\t\t// If we are working with a disconnected or an unowned signal that is now connected (due to an active effect)\n\t\t\t// then we need to re-connect the reaction to the dependency\n\t\t\tif (is_disconnected || is_unowned_connected) {\n\t\t\t\tvar derived = /** @type {Derived} */ (reaction);\n\t\t\t\tvar parent = derived.parent;\n\n\t\t\t\tfor (i = 0; i < length; i++) {\n\t\t\t\t\tdependency = dependencies[i];\n\n\t\t\t\t\t// We always re-add all reactions (even duplicates) if the derived was\n\t\t\t\t\t// previously disconnected, however we don't if it was unowned as we\n\t\t\t\t\t// de-duplicate dependencies in that case\n\t\t\t\t\tif (is_disconnected || !dependency?.reactions?.includes(derived)) {\n\t\t\t\t\t\t(dependency.reactions ??= []).push(derived);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (is_disconnected) {\n\t\t\t\t\tderived.f ^= DISCONNECTED;\n\t\t\t\t}\n\t\t\t\t// If the unowned derived is now fully connected to the graph again (it's unowned and reconnected, has a parent\n\t\t\t\t// and the parent is not unowned), then we can mark it as connected again, removing the need for the unowned\n\t\t\t\t// flag\n\t\t\t\tif (is_unowned_connected && parent !== null && (parent.f & UNOWNED) === 0) {\n\t\t\t\t\tderived.f ^= UNOWNED;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor (i = 0; i < length; i++) {\n\t\t\t\tdependency = dependencies[i];\n\n\t\t\t\tif (check_dirtiness(/** @type {Derived} */ (dependency))) {\n\t\t\t\t\tupdate_derived(/** @type {Derived} */ (dependency));\n\t\t\t\t}\n\n\t\t\t\tif (dependency.wv > reaction.wv) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Unowned signals should never be marked as clean unless they\n\t\t// are used within an active_effect without skip_reaction\n\t\tif (!is_unowned || (active_effect !== null && !skip_reaction)) {\n\t\t\tset_signal_status(reaction, CLEAN);\n\t\t}\n\t}\n\n\treturn false;\n}\n\n/**\n * @param {unknown} error\n * @param {Effect} effect\n */\nfunction propagate_error(error, effect) {\n\t/** @type {Effect | null} */\n\tvar current = effect;\n\n\twhile (current !== null) {\n\t\tif ((current.f & BOUNDARY_EFFECT) !== 0) {\n\t\t\ttry {\n\t\t\t\t// @ts-expect-error\n\t\t\t\tcurrent.fn(error);\n\t\t\t\treturn;\n\t\t\t} catch {\n\t\t\t\t// Remove boundary flag from effect\n\t\t\t\tcurrent.f ^= BOUNDARY_EFFECT;\n\t\t\t}\n\t\t}\n\n\t\tcurrent = current.parent;\n\t}\n\n\tis_throwing_error = false;\n\tthrow error;\n}\n\n/**\n * @param {Effect} effect\n */\nfunction should_rethrow_error(effect) {\n\treturn (\n\t\t(effect.f & DESTROYED) === 0 &&\n\t\t(effect.parent === null || (effect.parent.f & BOUNDARY_EFFECT) === 0)\n\t);\n}\n\nexport function reset_is_throwing_error() {\n\tis_throwing_error = false;\n}\n\n/**\n * @param {unknown} error\n * @param {Effect} effect\n * @param {Effect | null} previous_effect\n * @param {ComponentContext | null} component_context\n */\nexport function handle_error(error, effect, previous_effect, component_context) {\n\tif (is_throwing_error) {\n\t\tif (previous_effect === null) {\n\t\t\tis_throwing_error = false;\n\t\t}\n\n\t\tif (should_rethrow_error(effect)) {\n\t\t\tthrow error;\n\t\t}\n\n\t\treturn;\n\t}\n\n\tif (previous_effect !== null) {\n\t\tis_throwing_error = true;\n\t}\n\n\tif (DEV && component_context !== null && error instanceof Error && !handled_errors.has(error)) {\n\t\thandled_errors.add(error);\n\n\t\tconst component_stack = [];\n\n\t\tconst effect_name = effect.fn?.name;\n\n\t\tif (effect_name) {\n\t\t\tcomponent_stack.push(effect_name);\n\t\t}\n\n\t\t/** @type {ComponentContext | null} */\n\t\tlet current_context = component_context;\n\n\t\twhile (current_context !== null) {\n\t\t\t/** @type {string} */\n\t\t\tvar filename = current_context.function?.[FILENAME];\n\n\t\t\tif (filename) {\n\t\t\t\tconst file = filename.split('/').pop();\n\t\t\t\tcomponent_stack.push(file);\n\t\t\t}\n\n\t\t\tcurrent_context = current_context.p;\n\t\t}\n\n\t\tconst indent = is_firefox ? '  ' : '\\t';\n\t\tdefine_property(error, 'message', {\n\t\t\tvalue:\n\t\t\t\terror.message + `\\n${component_stack.map((name) => `\\n${indent}in ${name}`).join('')}\\n`\n\t\t});\n\t\tdefine_property(error, 'component_stack', {\n\t\t\tvalue: component_stack\n\t\t});\n\n\t\tconst stack = error.stack;\n\n\t\t// Filter out internal files from callstack\n\t\tif (stack) {\n\t\t\tconst lines = stack.split('\\n');\n\t\t\tconst new_lines = [];\n\t\t\tfor (let i = 0; i < lines.length; i++) {\n\t\t\t\tconst line = lines[i];\n\t\t\t\tif (line.includes('svelte/src/internal')) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tnew_lines.push(line);\n\t\t\t}\n\t\t\tdefine_property(error, 'stack', {\n\t\t\t\tvalue: new_lines.join('\\n')\n\t\t\t});\n\t\t}\n\t}\n\n\tpropagate_error(error, effect);\n\n\tif (should_rethrow_error(effect)) {\n\t\tthrow error;\n\t}\n}\n\n/**\n * @param {Value} signal\n * @param {Effect} effect\n * @param {boolean} [root]\n */\nfunction schedule_possible_effect_self_invalidation(signal, effect, root = true) {\n\tvar reactions = signal.reactions;\n\tif (reactions === null) return;\n\n\tfor (var i = 0; i < reactions.length; i++) {\n\t\tvar reaction = reactions[i];\n\n\t\tif (reaction_sources?.includes(signal)) continue;\n\n\t\tif ((reaction.f & DERIVED) !== 0) {\n\t\t\tschedule_possible_effect_self_invalidation(/** @type {Derived} */ (reaction), effect, false);\n\t\t} else if (effect === reaction) {\n\t\t\tif (root) {\n\t\t\t\tset_signal_status(reaction, DIRTY);\n\t\t\t} else if ((reaction.f & CLEAN) !== 0) {\n\t\t\t\tset_signal_status(reaction, MAYBE_DIRTY);\n\t\t\t}\n\t\t\tschedule_effect(/** @type {Effect} */ (reaction));\n\t\t}\n\t}\n}\n\n/**\n * @template V\n * @param {Reaction} reaction\n * @returns {V}\n */\nexport function update_reaction(reaction) {\n\tvar previous_deps = new_deps;\n\tvar previous_skipped_deps = skipped_deps;\n\tvar previous_untracked_writes = untracked_writes;\n\tvar previous_reaction = active_reaction;\n\tvar previous_skip_reaction = skip_reaction;\n\tvar previous_reaction_sources = reaction_sources;\n\tvar previous_component_context = component_context;\n\tvar previous_untracking = untracking;\n\n\tvar flags = reaction.f;\n\n\tnew_deps = /** @type {null | Value[]} */ (null);\n\tskipped_deps = 0;\n\tuntracked_writes = null;\n\tskip_reaction =\n\t\t(flags & UNOWNED) !== 0 && (untracking || !is_updating_effect || active_reaction === null);\n\tactive_reaction = (flags & (BRANCH_EFFECT | ROOT_EFFECT)) === 0 ? reaction : null;\n\n\treaction_sources = null;\n\tset_component_context(reaction.ctx);\n\tuntracking = false;\n\tread_version++;\n\n\treaction.f |= EFFECT_IS_UPDATING;\n\n\ttry {\n\t\tvar result = /** @type {Function} */ (0, reaction.fn)();\n\t\tvar deps = reaction.deps;\n\n\t\tif (new_deps !== null) {\n\t\t\tvar i;\n\n\t\t\tremove_reactions(reaction, skipped_deps);\n\n\t\t\tif (deps !== null && skipped_deps > 0) {\n\t\t\t\tdeps.length = skipped_deps + new_deps.length;\n\t\t\t\tfor (i = 0; i < new_deps.length; i++) {\n\t\t\t\t\tdeps[skipped_deps + i] = new_deps[i];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\treaction.deps = deps = new_deps;\n\t\t\t}\n\n\t\t\tif (!skip_reaction) {\n\t\t\t\tfor (i = skipped_deps; i < deps.length; i++) {\n\t\t\t\t\t(deps[i].reactions ??= []).push(reaction);\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (deps !== null && skipped_deps < deps.length) {\n\t\t\tremove_reactions(reaction, skipped_deps);\n\t\t\tdeps.length = skipped_deps;\n\t\t}\n\n\t\t// If we're inside an effect and we have untracked writes, then we need to\n\t\t// ensure that if any of those untracked writes result in re-invalidation\n\t\t// of the current effect, then that happens accordingly\n\t\tif (\n\t\t\tis_runes() &&\n\t\t\tuntracked_writes !== null &&\n\t\t\t!untracking &&\n\t\t\tdeps !== null &&\n\t\t\t(reaction.f & (DERIVED | MAYBE_DIRTY | DIRTY)) === 0\n\t\t) {\n\t\t\tfor (i = 0; i < /** @type {Source[]} */ (untracked_writes).length; i++) {\n\t\t\t\tschedule_possible_effect_self_invalidation(\n\t\t\t\t\tuntracked_writes[i],\n\t\t\t\t\t/** @type {Effect} */ (reaction)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\t// If we are returning to an previous reaction then\n\t\t// we need to increment the read version to ensure that\n\t\t// any dependencies in this reaction aren't marked with\n\t\t// the same version\n\t\tif (previous_reaction !== null && previous_reaction !== reaction) {\n\t\t\tread_version++;\n\n\t\t\tif (untracked_writes !== null) {\n\t\t\t\tif (previous_untracked_writes === null) {\n\t\t\t\t\tprevious_untracked_writes = untracked_writes;\n\t\t\t\t} else {\n\t\t\t\t\tprevious_untracked_writes.push(.../** @type {Source[]} */ (untracked_writes));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t} finally {\n\t\tnew_deps = previous_deps;\n\t\tskipped_deps = previous_skipped_deps;\n\t\tuntracked_writes = previous_untracked_writes;\n\t\tactive_reaction = previous_reaction;\n\t\tskip_reaction = previous_skip_reaction;\n\t\treaction_sources = previous_reaction_sources;\n\t\tset_component_context(previous_component_context);\n\t\tuntracking = previous_untracking;\n\n\t\treaction.f ^= EFFECT_IS_UPDATING;\n\t}\n}\n\n/**\n * @template V\n * @param {Reaction} signal\n * @param {Value<V>} dependency\n * @returns {void}\n */\nfunction remove_reaction(signal, dependency) {\n\tlet reactions = dependency.reactions;\n\tif (reactions !== null) {\n\t\tvar index = index_of.call(reactions, signal);\n\t\tif (index !== -1) {\n\t\t\tvar new_length = reactions.length - 1;\n\t\t\tif (new_length === 0) {\n\t\t\t\treactions = dependency.reactions = null;\n\t\t\t} else {\n\t\t\t\t// Swap with last element and then remove.\n\t\t\t\treactions[index] = reactions[new_length];\n\t\t\t\treactions.pop();\n\t\t\t}\n\t\t}\n\t}\n\t// If the derived has no reactions, then we can disconnect it from the graph,\n\t// allowing it to either reconnect in the future, or be GC'd by the VM.\n\tif (\n\t\treactions === null &&\n\t\t(dependency.f & DERIVED) !== 0 &&\n\t\t// Destroying a child effect while updating a parent effect can cause a dependency to appear\n\t\t// to be unused, when in fact it is used by the currently-updating parent. Checking `new_deps`\n\t\t// allows us to skip the expensive work of disconnecting and immediately reconnecting it\n\t\t(new_deps === null || !new_deps.includes(dependency))\n\t) {\n\t\tset_signal_status(dependency, MAYBE_DIRTY);\n\t\t// If we are working with a derived that is owned by an effect, then mark it as being\n\t\t// disconnected.\n\t\tif ((dependency.f & (UNOWNED | DISCONNECTED)) === 0) {\n\t\t\tdependency.f ^= DISCONNECTED;\n\t\t}\n\t\t// Disconnect any reactions owned by this reaction\n\t\tdestroy_derived_effects(/** @type {Derived} **/ (dependency));\n\t\tremove_reactions(/** @type {Derived} **/ (dependency), 0);\n\t}\n}\n\n/**\n * @param {Reaction} signal\n * @param {number} start_index\n * @returns {void}\n */\nexport function remove_reactions(signal, start_index) {\n\tvar dependencies = signal.deps;\n\tif (dependencies === null) return;\n\n\tfor (var i = start_index; i < dependencies.length; i++) {\n\t\tremove_reaction(signal, dependencies[i]);\n\t}\n}\n\n/**\n * @param {Effect} effect\n * @returns {void}\n */\nexport function update_effect(effect) {\n\tvar flags = effect.f;\n\n\tif ((flags & DESTROYED) !== 0) {\n\t\treturn;\n\t}\n\n\tset_signal_status(effect, CLEAN);\n\n\tvar previous_effect = active_effect;\n\tvar previous_component_context = component_context;\n\tvar was_updating_effect = is_updating_effect;\n\n\tactive_effect = effect;\n\tis_updating_effect = true;\n\n\tif (DEV) {\n\t\tvar previous_component_fn = dev_current_component_function;\n\t\tset_dev_current_component_function(effect.component_function);\n\t}\n\n\ttry {\n\t\tif ((flags & BLOCK_EFFECT) !== 0) {\n\t\t\tdestroy_block_effect_children(effect);\n\t\t} else {\n\t\t\tdestroy_effect_children(effect);\n\t\t}\n\n\t\texecute_effect_teardown(effect);\n\t\tvar teardown = update_reaction(effect);\n\t\teffect.teardown = typeof teardown === 'function' ? teardown : null;\n\t\teffect.wv = write_version;\n\n\t\tvar deps = effect.deps;\n\n\t\t// In DEV, we need to handle a case where $inspect.trace() might\n\t\t// incorrectly state a source dependency has not changed when it has.\n\t\t// That's beacuse that source was changed by the same effect, causing\n\t\t// the versions to match. We can avoid this by incrementing the version\n\t\tif (DEV && tracing_mode_flag && (effect.f & DIRTY) !== 0 && deps !== null) {\n\t\t\tfor (let i = 0; i < deps.length; i++) {\n\t\t\t\tvar dep = deps[i];\n\t\t\t\tif (dep.trace_need_increase) {\n\t\t\t\t\tdep.wv = increment_write_version();\n\t\t\t\t\tdep.trace_need_increase = undefined;\n\t\t\t\t\tdep.trace_v = undefined;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (DEV) {\n\t\t\tdev_effect_stack.push(effect);\n\t\t}\n\t} catch (error) {\n\t\thandle_error(error, effect, previous_effect, previous_component_context || effect.ctx);\n\t} finally {\n\t\tis_updating_effect = was_updating_effect;\n\t\tactive_effect = previous_effect;\n\n\t\tif (DEV) {\n\t\t\tset_dev_current_component_function(previous_component_fn);\n\t\t}\n\t}\n}\n\nfunction log_effect_stack() {\n\t// eslint-disable-next-line no-console\n\tconsole.error(\n\t\t'Last ten effects were: ',\n\t\tdev_effect_stack.slice(-10).map((d) => d.fn)\n\t);\n\tdev_effect_stack = [];\n}\n\nfunction infinite_loop_guard() {\n\ttry {\n\t\te.effect_update_depth_exceeded();\n\t} catch (error) {\n\t\tif (DEV) {\n\t\t\t// stack is garbage, ignore. Instead add a console.error message.\n\t\t\tdefine_property(error, 'stack', {\n\t\t\t\tvalue: ''\n\t\t\t});\n\t\t}\n\t\t// Try and handle the error so it can be caught at a boundary, that's\n\t\t// if there's an effect available from when it was last scheduled\n\t\tif (last_scheduled_effect !== null) {\n\t\t\tif (DEV) {\n\t\t\t\ttry {\n\t\t\t\t\thandle_error(error, last_scheduled_effect, null, null);\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// Only log the effect stack if the error is re-thrown\n\t\t\t\t\tlog_effect_stack();\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thandle_error(error, last_scheduled_effect, null, null);\n\t\t\t}\n\t\t} else {\n\t\t\tif (DEV) {\n\t\t\t\tlog_effect_stack();\n\t\t\t}\n\t\t\tthrow error;\n\t\t}\n\t}\n}\n\nfunction flush_queued_root_effects() {\n\tvar was_updating_effect = is_updating_effect;\n\n\ttry {\n\t\tvar flush_count = 0;\n\t\tis_updating_effect = true;\n\n\t\twhile (queued_root_effects.length > 0) {\n\t\t\tif (flush_count++ > 1000) {\n\t\t\t\tinfinite_loop_guard();\n\t\t\t}\n\n\t\t\tvar root_effects = queued_root_effects;\n\t\t\tvar length = root_effects.length;\n\n\t\t\tqueued_root_effects = [];\n\n\t\t\tfor (var i = 0; i < length; i++) {\n\t\t\t\tvar collected_effects = process_effects(root_effects[i]);\n\t\t\t\tflush_queued_effects(collected_effects);\n\t\t\t}\n\t\t\told_values.clear();\n\t\t}\n\t} finally {\n\t\tis_flushing = false;\n\t\tis_updating_effect = was_updating_effect;\n\n\t\tlast_scheduled_effect = null;\n\t\tif (DEV) {\n\t\t\tdev_effect_stack = [];\n\t\t}\n\t}\n}\n\n/**\n * @param {Array<Effect>} effects\n * @returns {void}\n */\nfunction flush_queued_effects(effects) {\n\tvar length = effects.length;\n\tif (length === 0) return;\n\n\tfor (var i = 0; i < length; i++) {\n\t\tvar effect = effects[i];\n\n\t\tif ((effect.f & (DESTROYED | INERT)) === 0) {\n\t\t\ttry {\n\t\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\t\tupdate_effect(effect);\n\n\t\t\t\t\t// Effects with no dependencies or teardown do not get added to the effect tree.\n\t\t\t\t\t// Deferred effects (e.g. `$effect(...)`) _are_ added to the tree because we\n\t\t\t\t\t// don't know if we need to keep them until they are executed. Doing the check\n\t\t\t\t\t// here (rather than in `update_effect`) allows us to skip the work for\n\t\t\t\t\t// immediate effects.\n\t\t\t\t\tif (effect.deps === null && effect.first === null && effect.nodes_start === null) {\n\t\t\t\t\t\tif (effect.teardown === null) {\n\t\t\t\t\t\t\t// remove this effect from the graph\n\t\t\t\t\t\t\tunlink_effect(effect);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// keep the effect in the graph, but free up some memory\n\t\t\t\t\t\t\teffect.fn = null;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\thandle_error(error, effect, null, effect.ctx);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * @param {Effect} signal\n * @returns {void}\n */\nexport function schedule_effect(signal) {\n\tif (!is_flushing) {\n\t\tis_flushing = true;\n\t\tqueueMicrotask(flush_queued_root_effects);\n\t}\n\n\tvar effect = (last_scheduled_effect = signal);\n\n\twhile (effect.parent !== null) {\n\t\teffect = effect.parent;\n\t\tvar flags = effect.f;\n\n\t\tif ((flags & (ROOT_EFFECT | BRANCH_EFFECT)) !== 0) {\n\t\t\tif ((flags & CLEAN) === 0) return;\n\t\t\teffect.f ^= CLEAN;\n\t\t}\n\t}\n\n\tqueued_root_effects.push(effect);\n}\n\n/**\n *\n * This function both runs render effects and collects user effects in topological order\n * from the starting effect passed in. Effects will be collected when they match the filtered\n * bitwise flag passed in only. The collected effects array will be populated with all the user\n * effects to be flushed.\n *\n * @param {Effect} root\n * @returns {Effect[]}\n */\nfunction process_effects(root) {\n\t/** @type {Effect[]} */\n\tvar effects = [];\n\n\t/** @type {Effect | null} */\n\tvar effect = root;\n\n\twhile (effect !== null) {\n\t\tvar flags = effect.f;\n\t\tvar is_branch = (flags & (BRANCH_EFFECT | ROOT_EFFECT)) !== 0;\n\t\tvar is_skippable_branch = is_branch && (flags & CLEAN) !== 0;\n\n\t\tif (!is_skippable_branch && (flags & INERT) === 0) {\n\t\t\tif ((flags & EFFECT) !== 0) {\n\t\t\t\teffects.push(effect);\n\t\t\t} else if (is_branch) {\n\t\t\t\teffect.f ^= CLEAN;\n\t\t\t} else {\n\t\t\t\ttry {\n\t\t\t\t\tif (check_dirtiness(effect)) {\n\t\t\t\t\t\tupdate_effect(effect);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\thandle_error(error, effect, null, effect.ctx);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/** @type {Effect | null} */\n\t\t\tvar child = effect.first;\n\n\t\t\tif (child !== null) {\n\t\t\t\teffect = child;\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t}\n\n\t\tvar parent = effect.parent;\n\t\teffect = effect.next;\n\n\t\twhile (effect === null && parent !== null) {\n\t\t\teffect = parent.next;\n\t\t\tparent = parent.parent;\n\t\t}\n\t}\n\n\treturn effects;\n}\n\n/**\n * Synchronously flush any pending updates.\n * Returns void if no callback is provided, otherwise returns the result of calling the callback.\n * @template [T=void]\n * @param {(() => T) | undefined} [fn]\n * @returns {T}\n */\nexport function flushSync(fn) {\n\tvar result;\n\n\tif (fn) {\n\t\tis_flushing = true;\n\t\tflush_queued_root_effects();\n\n\t\tis_flushing = true;\n\t\tresult = fn();\n\t}\n\n\twhile (true) {\n\t\tflush_tasks();\n\n\t\tif (queued_root_effects.length === 0) {\n\t\t\treturn /** @type {T} */ (result);\n\t\t}\n\n\t\tis_flushing = true;\n\t\tflush_queued_root_effects();\n\t}\n}\n\n/**\n * Returns a promise that resolves once any pending state changes have been applied.\n * @returns {Promise<void>}\n */\nexport async function tick() {\n\tawait Promise.resolve();\n\t// By calling flushSync we guarantee that any pending state changes are applied after one tick.\n\t// TODO look into whether we can make flushing subsequent updates synchronously in the future.\n\tflushSync();\n}\n\n/**\n * @template V\n * @param {Value<V>} signal\n * @returns {V}\n */\nexport function get(signal) {\n\tvar flags = signal.f;\n\tvar is_derived = (flags & DERIVED) !== 0;\n\n\tif (captured_signals !== null) {\n\t\tcaptured_signals.add(signal);\n\t}\n\n\t// Register the dependency on the current reaction signal.\n\tif (active_reaction !== null && !untracking) {\n\t\tif (!reaction_sources?.includes(signal)) {\n\t\t\tvar deps = active_reaction.deps;\n\t\t\tif (signal.rv < read_version) {\n\t\t\t\tsignal.rv = read_version;\n\t\t\t\t// If the signal is accessing the same dependencies in the same\n\t\t\t\t// order as it did last time, increment `skipped_deps`\n\t\t\t\t// rather than updating `new_deps`, which creates GC cost\n\t\t\t\tif (new_deps === null && deps !== null && deps[skipped_deps] === signal) {\n\t\t\t\t\tskipped_deps++;\n\t\t\t\t} else if (new_deps === null) {\n\t\t\t\t\tnew_deps = [signal];\n\t\t\t\t} else if (!skip_reaction || !new_deps.includes(signal)) {\n\t\t\t\t\t// Normally we can push duplicated dependencies to `new_deps`, but if we're inside\n\t\t\t\t\t// an unowned derived because skip_reaction is true, then we need to ensure that\n\t\t\t\t\t// we don't have duplicates\n\t\t\t\t\tnew_deps.push(signal);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else if (\n\t\tis_derived &&\n\t\t/** @type {Derived} */ (signal).deps === null &&\n\t\t/** @type {Derived} */ (signal).effects === null\n\t) {\n\t\tvar derived = /** @type {Derived} */ (signal);\n\t\tvar parent = derived.parent;\n\n\t\tif (parent !== null && (parent.f & UNOWNED) === 0) {\n\t\t\t// If the derived is owned by another derived then mark it as unowned\n\t\t\t// as the derived value might have been referenced in a different context\n\t\t\t// since and thus its parent might not be its true owner anymore\n\t\t\tderived.f ^= UNOWNED;\n\t\t}\n\t}\n\n\tif (is_derived) {\n\t\tderived = /** @type {Derived} */ (signal);\n\n\t\tif (check_dirtiness(derived)) {\n\t\t\tupdate_derived(derived);\n\t\t}\n\t}\n\n\tif (\n\t\tDEV &&\n\t\ttracing_mode_flag &&\n\t\ttracing_expressions !== null &&\n\t\tactive_reaction !== null &&\n\t\ttracing_expressions.reaction === active_reaction\n\t) {\n\t\t// Used when mapping state between special blocks like `each`\n\t\tif (signal.debug) {\n\t\t\tsignal.debug();\n\t\t} else if (signal.created) {\n\t\t\tvar entry = tracing_expressions.entries.get(signal);\n\n\t\t\tif (entry === undefined) {\n\t\t\t\tentry = { read: [] };\n\t\t\t\ttracing_expressions.entries.set(signal, entry);\n\t\t\t}\n\n\t\t\tentry.read.push(get_stack('TracedAt'));\n\t\t}\n\t}\n\n\tif (is_destroying_effect && old_values.has(signal)) {\n\t\treturn old_values.get(signal);\n\t}\n\n\treturn signal.v;\n}\n\n/**\n * Like `get`, but checks for `undefined`. Used for `var` declarations because they can be accessed before being declared\n * @template V\n * @param {Value<V> | undefined} signal\n * @returns {V | undefined}\n */\nexport function safe_get(signal) {\n\treturn signal && get(signal);\n}\n\n/**\n * Capture an array of all the signals that are read when `fn` is called\n * @template T\n * @param {() => T} fn\n */\nfunction capture_signals(fn) {\n\tvar previous_captured_signals = captured_signals;\n\tcaptured_signals = new Set();\n\n\tvar captured = captured_signals;\n\tvar signal;\n\n\ttry {\n\t\tuntrack(fn);\n\t\tif (previous_captured_signals !== null) {\n\t\t\tfor (signal of captured_signals) {\n\t\t\t\tprevious_captured_signals.add(signal);\n\t\t\t}\n\t\t}\n\t} finally {\n\t\tcaptured_signals = previous_captured_signals;\n\t}\n\n\treturn captured;\n}\n\n/**\n * Invokes a function and captures all signals that are read during the invocation,\n * then invalidates them.\n * @param {() => any} fn\n */\nexport function invalidate_inner_signals(fn) {\n\tvar captured = capture_signals(() => untrack(fn));\n\n\tfor (var signal of captured) {\n\t\t// Go one level up because derived signals created as part of props in legacy mode\n\t\tif ((signal.f & LEGACY_DERIVED_PROP) !== 0) {\n\t\t\tfor (const dep of /** @type {Derived} */ (signal).deps || []) {\n\t\t\t\tif ((dep.f & DERIVED) === 0) {\n\t\t\t\t\t// Use internal_set instead of set here and below to avoid mutation validation\n\t\t\t\t\tinternal_set(dep, dep.v);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tinternal_set(signal, signal.v);\n\t\t}\n\t}\n}\n\n/**\n * When used inside a [`$derived`](https://svelte.dev/docs/svelte/$derived) or [`$effect`](https://svelte.dev/docs/svelte/$effect),\n * any state read inside `fn` will not be treated as a dependency.\n *\n * ```ts\n * $effect(() => {\n *   // this will run when `data` changes, but not when `time` changes\n *   save(data, {\n *     timestamp: untrack(() => time)\n *   });\n * });\n * ```\n * @template T\n * @param {() => T} fn\n * @returns {T}\n */\nexport function untrack(fn) {\n\tvar previous_untracking = untracking;\n\ttry {\n\t\tuntracking = true;\n\t\treturn fn();\n\t} finally {\n\t\tuntracking = previous_untracking;\n\t}\n}\n\nconst STATUS_MASK = ~(DIRTY | MAYBE_DIRTY | CLEAN);\n\n/**\n * @param {Signal} signal\n * @param {number} status\n * @returns {void}\n */\nexport function set_signal_status(signal, status) {\n\tsignal.f = (signal.f & STATUS_MASK) | status;\n}\n\n/**\n * @param {Record<string, unknown>} obj\n * @param {string[]} keys\n * @returns {Record<string, unknown>}\n */\nexport function exclude_from_object(obj, keys) {\n\t/** @type {Record<string, unknown>} */\n\tvar result = {};\n\n\tfor (var key in obj) {\n\t\tif (!keys.includes(key)) {\n\t\t\tresult[key] = obj[key];\n\t\t}\n\t}\n\n\treturn result;\n}\n\n/**\n * Possibly traverse an object and read all its properties so that they're all reactive in case this is `$state`.\n * Does only check first level of an object for performance reasons (heuristic should be good for 99% of all cases).\n * @param {any} value\n * @returns {void}\n */\nexport function deep_read_state(value) {\n\tif (typeof value !== 'object' || !value || value instanceof EventTarget) {\n\t\treturn;\n\t}\n\n\tif (STATE_SYMBOL in value) {\n\t\tdeep_read(value);\n\t} else if (!Array.isArray(value)) {\n\t\tfor (let key in value) {\n\t\t\tconst prop = value[key];\n\t\t\tif (typeof prop === 'object' && prop && STATE_SYMBOL in prop) {\n\t\t\t\tdeep_read(prop);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * Deeply traverse an object and read all its properties\n * so that they're all reactive in case this is `$state`\n * @param {any} value\n * @param {Set<any>} visited\n * @returns {void}\n */\nexport function deep_read(value, visited = new Set()) {\n\tif (\n\t\ttypeof value === 'object' &&\n\t\tvalue !== null &&\n\t\t// We don't want to traverse DOM elements\n\t\t!(value instanceof EventTarget) &&\n\t\t!visited.has(value)\n\t) {\n\t\tvisited.add(value);\n\t\t// When working with a possible SvelteDate, this\n\t\t// will ensure we capture changes to it.\n\t\tif (value instanceof Date) {\n\t\t\tvalue.getTime();\n\t\t}\n\t\tfor (let key in value) {\n\t\t\ttry {\n\t\t\t\tdeep_read(value[key], visited);\n\t\t\t} catch (e) {\n\t\t\t\t// continue\n\t\t\t}\n\t\t}\n\t\tconst proto = get_prototype_of(value);\n\t\tif (\n\t\t\tproto !== Object.prototype &&\n\t\t\tproto !== Array.prototype &&\n\t\t\tproto !== Map.prototype &&\n\t\t\tproto !== Set.prototype &&\n\t\t\tproto !== Date.prototype\n\t\t) {\n\t\t\tconst descriptors = get_descriptors(proto);\n\t\t\tfor (let key in descriptors) {\n\t\t\t\tconst get = descriptors[key].get;\n\t\t\t\tif (get) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tget.call(value);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t// continue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "const regex_return_characters = /\\r/g;\n\n/**\n * @param {string} str\n * @returns {string}\n */\nexport function hash(str) {\n\tstr = str.replace(regex_return_characters, '');\n\tlet hash = 5381;\n\tlet i = str.length;\n\n\twhile (i--) hash = ((hash << 5) - hash) ^ str.charCodeAt(i);\n\treturn (hash >>> 0).toString(36);\n}\n\nconst VOID_ELEMENT_NAMES = [\n\t'area',\n\t'base',\n\t'br',\n\t'col',\n\t'command',\n\t'embed',\n\t'hr',\n\t'img',\n\t'input',\n\t'keygen',\n\t'link',\n\t'meta',\n\t'param',\n\t'source',\n\t'track',\n\t'wbr'\n];\n\n/**\n * Returns `true` if `name` is of a void element\n * @param {string} name\n */\nexport function is_void(name) {\n\treturn VOID_ELEMENT_NAMES.includes(name) || name.toLowerCase() === '!doctype';\n}\n\nconst RESERVED_WORDS = [\n\t'arguments',\n\t'await',\n\t'break',\n\t'case',\n\t'catch',\n\t'class',\n\t'const',\n\t'continue',\n\t'debugger',\n\t'default',\n\t'delete',\n\t'do',\n\t'else',\n\t'enum',\n\t'eval',\n\t'export',\n\t'extends',\n\t'false',\n\t'finally',\n\t'for',\n\t'function',\n\t'if',\n\t'implements',\n\t'import',\n\t'in',\n\t'instanceof',\n\t'interface',\n\t'let',\n\t'new',\n\t'null',\n\t'package',\n\t'private',\n\t'protected',\n\t'public',\n\t'return',\n\t'static',\n\t'super',\n\t'switch',\n\t'this',\n\t'throw',\n\t'true',\n\t'try',\n\t'typeof',\n\t'var',\n\t'void',\n\t'while',\n\t'with',\n\t'yield'\n];\n\n/**\n * Returns `true` if `word` is a reserved JavaScript keyword\n * @param {string} word\n */\nexport function is_reserved(word) {\n\treturn RESERVED_WORDS.includes(word);\n}\n\n/**\n * @param {string} name\n */\nexport function is_capture_event(name) {\n\treturn name.endsWith('capture') && name !== 'gotpointercapture' && name !== 'lostpointercapture';\n}\n\n/** List of Element events that will be delegated */\nconst DELEGATED_EVENTS = [\n\t'beforeinput',\n\t'click',\n\t'change',\n\t'dblclick',\n\t'contextmenu',\n\t'focusin',\n\t'focusout',\n\t'input',\n\t'keydown',\n\t'keyup',\n\t'mousedown',\n\t'mousemove',\n\t'mouseout',\n\t'mouseover',\n\t'mouseup',\n\t'pointerdown',\n\t'pointermove',\n\t'pointerout',\n\t'pointerover',\n\t'pointerup',\n\t'touchend',\n\t'touchmove',\n\t'touchstart'\n];\n\n/**\n * Returns `true` if `event_name` is a delegated event\n * @param {string} event_name\n */\nexport function is_delegated(event_name) {\n\treturn DELEGATED_EVENTS.includes(event_name);\n}\n\n/**\n * Attributes that are boolean, i.e. they are present or not present.\n */\nconst DOM_BOOLEAN_ATTRIBUTES = [\n\t'allowfullscreen',\n\t'async',\n\t'autofocus',\n\t'autoplay',\n\t'checked',\n\t'controls',\n\t'default',\n\t'disabled',\n\t'formnovalidate',\n\t'hidden',\n\t'indeterminate',\n\t'inert',\n\t'ismap',\n\t'loop',\n\t'multiple',\n\t'muted',\n\t'nomodule',\n\t'novalidate',\n\t'open',\n\t'playsinline',\n\t'readonly',\n\t'required',\n\t'reversed',\n\t'seamless',\n\t'selected',\n\t'webkitdirectory',\n\t'defer',\n\t'disablepictureinpicture',\n\t'disableremoteplayback'\n];\n\n/**\n * Returns `true` if `name` is a boolean attribute\n * @param {string} name\n */\nexport function is_boolean_attribute(name) {\n\treturn DOM_BOOLEAN_ATTRIBUTES.includes(name);\n}\n\n/**\n * @type {Record<string, string>}\n * List of attribute names that should be aliased to their property names\n * because they behave differently between setting them as an attribute and\n * setting them as a property.\n */\nconst ATTRIBUTE_ALIASES = {\n\t// no `class: 'className'` because we handle that separately\n\tformnovalidate: 'formNoValidate',\n\tismap: 'isMap',\n\tnomodule: 'noModule',\n\tplaysinline: 'playsInline',\n\treadonly: 'readOnly',\n\tdefaultvalue: 'defaultValue',\n\tdefaultchecked: 'defaultChecked',\n\tsrcobject: 'srcObject',\n\tnovalidate: 'noValidate',\n\tallowfullscreen: 'allowFullscreen',\n\tdisablepictureinpicture: 'disablePictureInPicture',\n\tdisableremoteplayback: 'disableRemotePlayback'\n};\n\n/**\n * @param {string} name\n */\nexport function normalize_attribute(name) {\n\tname = name.toLowerCase();\n\treturn ATTRIBUTE_ALIASES[name] ?? name;\n}\n\nconst DOM_PROPERTIES = [\n\t...DOM_BOOLEAN_ATTRIBUTES,\n\t'formNoValidate',\n\t'isMap',\n\t'noModule',\n\t'playsInline',\n\t'readOnly',\n\t'value',\n\t'volume',\n\t'defaultValue',\n\t'defaultChecked',\n\t'srcObject',\n\t'noValidate',\n\t'allowFullscreen',\n\t'disablePictureInPicture',\n\t'disableRemotePlayback'\n];\n\n/**\n * @param {string} name\n */\nexport function is_dom_property(name) {\n\treturn DOM_PROPERTIES.includes(name);\n}\n\nconst NON_STATIC_PROPERTIES = ['autofocus', 'muted', 'defaultValue', 'defaultChecked'];\n\n/**\n * Returns `true` if the given attribute cannot be set through the template\n * string, i.e. needs some kind of JavaScript handling to work.\n * @param {string} name\n */\nexport function cannot_be_set_statically(name) {\n\treturn NON_STATIC_PROPERTIES.includes(name);\n}\n\n/**\n * Subset of delegated events which should be passive by default.\n * These two are already passive via browser defaults on window, document and body.\n * But since\n * - we're delegating them\n * - they happen often\n * - they apply to mobile which is generally less performant\n * we're marking them as passive by default for other elements, too.\n */\nconst PASSIVE_EVENTS = ['touchstart', 'touchmove'];\n\n/**\n * Returns `true` if `name` is a passive event\n * @param {string} name\n */\nexport function is_passive_event(name) {\n\treturn PASSIVE_EVENTS.includes(name);\n}\n\nconst CONTENT_EDITABLE_BINDINGS = ['textContent', 'innerHTML', 'innerText'];\n\n/** @param {string} name */\nexport function is_content_editable_binding(name) {\n\treturn CONTENT_EDITABLE_BINDINGS.includes(name);\n}\n\nconst LOAD_ERROR_ELEMENTS = [\n\t'body',\n\t'embed',\n\t'iframe',\n\t'img',\n\t'link',\n\t'object',\n\t'script',\n\t'style',\n\t'track'\n];\n\n/**\n * Returns `true` if the element emits `load` and `error` events\n * @param {string} name\n */\nexport function is_load_error_element(name) {\n\treturn LOAD_ERROR_ELEMENTS.includes(name);\n}\n\nconst SVG_ELEMENTS = [\n\t'altGlyph',\n\t'altGlyphDef',\n\t'altGlyphItem',\n\t'animate',\n\t'animateColor',\n\t'animateMotion',\n\t'animateTransform',\n\t'circle',\n\t'clipPath',\n\t'color-profile',\n\t'cursor',\n\t'defs',\n\t'desc',\n\t'discard',\n\t'ellipse',\n\t'feBlend',\n\t'feColorMatrix',\n\t'feComponentTransfer',\n\t'feComposite',\n\t'feConvolveMatrix',\n\t'feDiffuseLighting',\n\t'feDisplacementMap',\n\t'feDistantLight',\n\t'feDropShadow',\n\t'feFlood',\n\t'feFuncA',\n\t'feFuncB',\n\t'feFuncG',\n\t'feFuncR',\n\t'feGaussianBlur',\n\t'feImage',\n\t'feMerge',\n\t'feMergeNode',\n\t'feMorphology',\n\t'feOffset',\n\t'fePointLight',\n\t'feSpecularLighting',\n\t'feSpotLight',\n\t'feTile',\n\t'feTurbulence',\n\t'filter',\n\t'font',\n\t'font-face',\n\t'font-face-format',\n\t'font-face-name',\n\t'font-face-src',\n\t'font-face-uri',\n\t'foreignObject',\n\t'g',\n\t'glyph',\n\t'glyphRef',\n\t'hatch',\n\t'hatchpath',\n\t'hkern',\n\t'image',\n\t'line',\n\t'linearGradient',\n\t'marker',\n\t'mask',\n\t'mesh',\n\t'meshgradient',\n\t'meshpatch',\n\t'meshrow',\n\t'metadata',\n\t'missing-glyph',\n\t'mpath',\n\t'path',\n\t'pattern',\n\t'polygon',\n\t'polyline',\n\t'radialGradient',\n\t'rect',\n\t'set',\n\t'solidcolor',\n\t'stop',\n\t'svg',\n\t'switch',\n\t'symbol',\n\t'text',\n\t'textPath',\n\t'tref',\n\t'tspan',\n\t'unknown',\n\t'use',\n\t'view',\n\t'vkern'\n];\n\n/** @param {string} name */\nexport function is_svg(name) {\n\treturn SVG_ELEMENTS.includes(name);\n}\n\nconst MATHML_ELEMENTS = [\n\t'annotation',\n\t'annotation-xml',\n\t'maction',\n\t'math',\n\t'merror',\n\t'mfrac',\n\t'mi',\n\t'mmultiscripts',\n\t'mn',\n\t'mo',\n\t'mover',\n\t'mpadded',\n\t'mphantom',\n\t'mprescripts',\n\t'mroot',\n\t'mrow',\n\t'ms',\n\t'mspace',\n\t'msqrt',\n\t'mstyle',\n\t'msub',\n\t'msubsup',\n\t'msup',\n\t'mtable',\n\t'mtd',\n\t'mtext',\n\t'mtr',\n\t'munder',\n\t'munderover',\n\t'semantics'\n];\n\n/** @param {string} name */\nexport function is_mathml(name) {\n\treturn MATHML_ELEMENTS.includes(name);\n}\n\nexport const STATE_CREATION_RUNES = /** @type {const} */ ([\n\t'$state',\n\t'$state.raw',\n\t'$derived',\n\t'$derived.by'\n]);\n\nconst RUNES = /** @type {const} */ ([\n\t...STATE_CREATION_RUNES,\n\t'$state.snapshot',\n\t'$props',\n\t'$props.id',\n\t'$bindable',\n\t'$effect',\n\t'$effect.pre',\n\t'$effect.tracking',\n\t'$effect.root',\n\t'$inspect',\n\t'$inspect().with',\n\t'$inspect.trace',\n\t'$host'\n]);\n\n/** @typedef {RUNES[number]} RuneName */\n\n/**\n * @param {string} name\n * @returns {name is RuneName}\n */\nexport function is_rune(name) {\n\treturn RUNES.includes(/** @type {RuneName} */ (name));\n}\n\n/** @typedef {STATE_CREATION_RUNES[number]} StateCreationRuneName */\n\n/**\n * @param {string} name\n * @returns {name is StateCreationRuneName}\n */\nexport function is_state_creation_rune(name) {\n\treturn STATE_CREATION_RUNES.includes(/** @type {StateCreationRuneName} */ (name));\n}\n\n/** List of elements that require raw contents and should not have SSR comments put in them */\nconst RAW_TEXT_ELEMENTS = /** @type {const} */ (['textarea', 'script', 'style', 'title']);\n\n/** @param {string} name */\nexport function is_raw_text_element(name) {\n\treturn RAW_TEXT_ELEMENTS.includes(/** @type {RAW_TEXT_ELEMENTS[number]} */ (name));\n}\n\n/**\n * Prevent devtools trying to make `location` a clickable link by inserting a zero-width space\n * @template {string | undefined} T\n * @param {T} location\n * @returns {T};\n */\nexport function sanitize_location(location) {\n\treturn /** @type {T} */ (location?.replace(/\\//g, '/\\u200b'));\n}\n", "import { teardown } from '../../reactivity/effects.js';\nimport { define_property, is_array } from '../../../shared/utils.js';\nimport { hydrating } from '../hydration.js';\nimport { queue_micro_task } from '../task.js';\nimport { FILENAME } from '../../../../constants.js';\nimport * as w from '../../warnings.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../runtime.js';\nimport { without_reactive_context } from './bindings/shared.js';\n\n/** @type {Set<string>} */\nexport const all_registered_events = new Set();\n\n/** @type {Set<(events: Array<string>) => void>} */\nexport const root_event_handles = new Set();\n\n/**\n * SSR adds onload and onerror attributes to catch those events before the hydration.\n * This function detects those cases, removes the attributes and replays the events.\n * @param {HTMLElement} dom\n */\nexport function replay_events(dom) {\n\tif (!hydrating) return;\n\n\tdom.removeAttribute('onload');\n\tdom.removeAttribute('onerror');\n\t// @ts-expect-error\n\tconst event = dom.__e;\n\tif (event !== undefined) {\n\t\t// @ts-expect-error\n\t\tdom.__e = undefined;\n\t\tqueueMicrotask(() => {\n\t\t\tif (dom.isConnected) {\n\t\t\t\tdom.dispatchEvent(event);\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * @param {string} event_name\n * @param {EventTarget} dom\n * @param {EventListener} [handler]\n * @param {AddEventListenerOptions} [options]\n */\nexport function create_event(event_name, dom, handler, options = {}) {\n\t/**\n\t * @this {EventTarget}\n\t */\n\tfunction target_handler(/** @type {Event} */ event) {\n\t\tif (!options.capture) {\n\t\t\t// Only call in the bubble phase, else delegated events would be called before the capturing events\n\t\t\thandle_event_propagation.call(dom, event);\n\t\t}\n\t\tif (!event.cancelBubble) {\n\t\t\treturn without_reactive_context(() => {\n\t\t\t\treturn handler?.call(this, event);\n\t\t\t});\n\t\t}\n\t}\n\n\t// Chrome has a bug where pointer events don't work when attached to a DOM element that has been cloned\n\t// with cloneNode() and the DOM element is disconnected from the document. To ensure the event works, we\n\t// defer the attachment till after it's been appended to the document. TODO: remove this once Chrome fixes\n\t// this bug. The same applies to wheel events and touch events.\n\tif (\n\t\tevent_name.startsWith('pointer') ||\n\t\tevent_name.startsWith('touch') ||\n\t\tevent_name === 'wheel'\n\t) {\n\t\tqueue_micro_task(() => {\n\t\t\tdom.addEventListener(event_name, target_handler, options);\n\t\t});\n\t} else {\n\t\tdom.addEventListener(event_name, target_handler, options);\n\t}\n\n\treturn target_handler;\n}\n\n/**\n * Attaches an event handler to an element and returns a function that removes the handler. Using this\n * rather than `addEventListener` will preserve the correct order relative to handlers added declaratively\n * (with attributes like `onclick`), which use event delegation for performance reasons\n *\n * @param {EventTarget} element\n * @param {string} type\n * @param {EventListener} handler\n * @param {AddEventListenerOptions} [options]\n */\nexport function on(element, type, handler, options = {}) {\n\tvar target_handler = create_event(type, element, handler, options);\n\n\treturn () => {\n\t\telement.removeEventListener(type, target_handler, options);\n\t};\n}\n\n/**\n * @param {string} event_name\n * @param {Element} dom\n * @param {EventListener} [handler]\n * @param {boolean} [capture]\n * @param {boolean} [passive]\n * @returns {void}\n */\nexport function event(event_name, dom, handler, capture, passive) {\n\tvar options = { capture, passive };\n\tvar target_handler = create_event(event_name, dom, handler, options);\n\n\tif (\n\t\tdom === document.body ||\n\t\t// @ts-ignore\n\t\tdom === window ||\n\t\t// @ts-ignore\n\t\tdom === document ||\n\t\t// Firefox has quirky behavior, it can happen that we still get \"canplay\" events when the element is already removed\n\t\tdom instanceof HTMLMediaElement\n\t) {\n\t\tteardown(() => {\n\t\t\tdom.removeEventListener(event_name, target_handler, options);\n\t\t});\n\t}\n}\n\n/**\n * @param {Array<string>} events\n * @returns {void}\n */\nexport function delegate(events) {\n\tfor (var i = 0; i < events.length; i++) {\n\t\tall_registered_events.add(events[i]);\n\t}\n\n\tfor (var fn of root_event_handles) {\n\t\tfn(events);\n\t}\n}\n\n/**\n * @this {EventTarget}\n * @param {Event} event\n * @returns {void}\n */\nexport function handle_event_propagation(event) {\n\tvar handler_element = this;\n\tvar owner_document = /** @type {Node} */ (handler_element).ownerDocument;\n\tvar event_name = event.type;\n\tvar path = event.composedPath?.() || [];\n\tvar current_target = /** @type {null | Element} */ (path[0] || event.target);\n\n\t// composedPath contains list of nodes the event has propagated through.\n\t// We check __root to skip all nodes below it in case this is a\n\t// parent of the __root node, which indicates that there's nested\n\t// mounted apps. In this case we don't want to trigger events multiple times.\n\tvar path_idx = 0;\n\n\t// @ts-expect-error is added below\n\tvar handled_at = event.__root;\n\n\tif (handled_at) {\n\t\tvar at_idx = path.indexOf(handled_at);\n\t\tif (\n\t\t\tat_idx !== -1 &&\n\t\t\t(handler_element === document || handler_element === /** @type {any} */ (window))\n\t\t) {\n\t\t\t// This is the fallback document listener or a window listener, but the event was already handled\n\t\t\t// -> ignore, but set handle_at to document/window so that we're resetting the event\n\t\t\t// chain in case someone manually dispatches the same event object again.\n\t\t\t// @ts-expect-error\n\t\t\tevent.__root = handler_element;\n\t\t\treturn;\n\t\t}\n\n\t\t// We're deliberately not skipping if the index is higher, because\n\t\t// someone could create an event programmatically and emit it multiple times,\n\t\t// in which case we want to handle the whole propagation chain properly each time.\n\t\t// (this will only be a false negative if the event is dispatched multiple times and\n\t\t// the fallback document listener isn't reached in between, but that's super rare)\n\t\tvar handler_idx = path.indexOf(handler_element);\n\t\tif (handler_idx === -1) {\n\t\t\t// handle_idx can theoretically be -1 (happened in some JSDOM testing scenarios with an event listener on the window object)\n\t\t\t// so guard against that, too, and assume that everything was handled at this point.\n\t\t\treturn;\n\t\t}\n\n\t\tif (at_idx <= handler_idx) {\n\t\t\tpath_idx = at_idx;\n\t\t}\n\t}\n\n\tcurrent_target = /** @type {Element} */ (path[path_idx] || event.target);\n\t// there can only be one delegated event per element, and we either already handled the current target,\n\t// or this is the very first target in the chain which has a non-delegated listener, in which case it's safe\n\t// to handle a possible delegated event on it later (through the root delegation listener for example).\n\tif (current_target === handler_element) return;\n\n\t// Proxy currentTarget to correct target\n\tdefine_property(event, 'currentTarget', {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn current_target || owner_document;\n\t\t}\n\t});\n\n\t// This started because of Chromium issue https://chromestatus.com/feature/5128696823545856,\n\t// where removal or moving of of the DOM can cause sync `blur` events to fire, which can cause logic\n\t// to run inside the current `active_reaction`, which isn't what we want at all. However, on reflection,\n\t// it's probably best that all event handled by Svelte have this behaviour, as we don't really want\n\t// an event handler to run in the context of another reaction or effect.\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\n\ttry {\n\t\t/**\n\t\t * @type {unknown}\n\t\t */\n\t\tvar throw_error;\n\t\t/**\n\t\t * @type {unknown[]}\n\t\t */\n\t\tvar other_errors = [];\n\n\t\twhile (current_target !== null) {\n\t\t\t/** @type {null | Element} */\n\t\t\tvar parent_element =\n\t\t\t\tcurrent_target.assignedSlot ||\n\t\t\t\tcurrent_target.parentNode ||\n\t\t\t\t/** @type {any} */ (current_target).host ||\n\t\t\t\tnull;\n\n\t\t\ttry {\n\t\t\t\t// @ts-expect-error\n\t\t\t\tvar delegated = current_target['__' + event_name];\n\n\t\t\t\tif (\n\t\t\t\t\tdelegated != null &&\n\t\t\t\t\t(!(/** @type {any} */ (current_target).disabled) ||\n\t\t\t\t\t\t// DOM could've been updated already by the time this is reached, so we check this as well\n\t\t\t\t\t\t// -> the target could not have been disabled because it emits the event in the first place\n\t\t\t\t\t\tevent.target === current_target)\n\t\t\t\t) {\n\t\t\t\t\tif (is_array(delegated)) {\n\t\t\t\t\t\tvar [fn, ...data] = delegated;\n\t\t\t\t\t\tfn.apply(current_target, [event, ...data]);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdelegated.call(current_target, event);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (throw_error) {\n\t\t\t\t\tother_errors.push(error);\n\t\t\t\t} else {\n\t\t\t\t\tthrow_error = error;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (event.cancelBubble || parent_element === handler_element || parent_element === null) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tcurrent_target = parent_element;\n\t\t}\n\n\t\tif (throw_error) {\n\t\t\tfor (let error of other_errors) {\n\t\t\t\t// Throw the rest of the errors, one-by-one on a microtask\n\t\t\t\tqueueMicrotask(() => {\n\t\t\t\t\tthrow error;\n\t\t\t\t});\n\t\t\t}\n\t\t\tthrow throw_error;\n\t\t}\n\t} finally {\n\t\t// @ts-expect-error is used above\n\t\tevent.__root = handler_element;\n\t\t// @ts-ignore remove proxy on currentTarget\n\t\tdelete event.currentTarget;\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t}\n}\n\n/**\n * In dev, warn if an event handler is not a function, as it means the\n * user probably called the handler or forgot to add a `() =>`\n * @param {() => (event: Event, ...args: any) => void} thunk\n * @param {EventTarget} element\n * @param {[Event, ...any]} args\n * @param {any} component\n * @param {[number, number]} [loc]\n * @param {boolean} [remove_parens]\n */\nexport function apply(\n\tthunk,\n\telement,\n\targs,\n\tcomponent,\n\tloc,\n\thas_side_effects = false,\n\tremove_parens = false\n) {\n\tlet handler;\n\tlet error;\n\n\ttry {\n\t\thandler = thunk();\n\t} catch (e) {\n\t\terror = e;\n\t}\n\n\tif (typeof handler !== 'function' && (has_side_effects || handler != null || error)) {\n\t\tconst filename = component?.[FILENAME];\n\t\tconst location = loc ? ` at ${filename}:${loc[0]}:${loc[1]}` : ` in ${filename}`;\n\t\tconst phase = args[0]?.eventPhase < Event.BUBBLING_PHASE ? 'capture' : '';\n\t\tconst event_name = args[0]?.type + phase;\n\t\tconst description = `\\`${event_name}\\` handler${location}`;\n\t\tconst suggestion = remove_parens ? 'remove the trailing `()`' : 'add a leading `() =>`';\n\n\t\tw.event_handler_invalid(description, suggestion);\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\t}\n\thandler?.apply(element, args);\n}\n", "/** @import { TemplateNode } from '#client' */\nimport { hydrate_node, hydrating, set_hydrate_node, set_hydrating } from '../hydration.js';\nimport { create_text, get_first_child, get_next_sibling } from '../operations.js';\nimport { block } from '../../reactivity/effects.js';\nimport { HEAD_EFFECT } from '#client/constants';\nimport { HYDRATION_START } from '../../../../constants.js';\n\n/**\n * @type {Node | undefined}\n */\nlet head_anchor;\n\nexport function reset_head_anchor() {\n\thead_anchor = undefined;\n}\n\n/**\n * @param {(anchor: Node) => void} render_fn\n * @returns {void}\n */\nexport function head(render_fn) {\n\t// The head function may be called after the first hydration pass and ssr comment nodes may still be present,\n\t// therefore we need to skip that when we detect that we're not in hydration mode.\n\tlet previous_hydrate_node = null;\n\tlet was_hydrating = hydrating;\n\n\t/** @type {Comment | Text} */\n\tvar anchor;\n\n\tif (hydrating) {\n\t\tprevious_hydrate_node = hydrate_node;\n\n\t\t// There might be multiple head blocks in our app, so we need to account for each one needing independent hydration.\n\t\tif (head_anchor === undefined) {\n\t\t\thead_anchor = /** @type {TemplateNode} */ (get_first_child(document.head));\n\t\t}\n\n\t\twhile (\n\t\t\thead_anchor !== null &&\n\t\t\t(head_anchor.nodeType !== 8 || /** @type {Comment} */ (head_anchor).data !== HYDRATION_START)\n\t\t) {\n\t\t\thead_anchor = /** @type {TemplateNode} */ (get_next_sibling(head_anchor));\n\t\t}\n\n\t\t// If we can't find an opening hydration marker, skip hydration (this can happen\n\t\t// if a framework rendered body but not head content)\n\t\tif (head_anchor === null) {\n\t\t\tset_hydrating(false);\n\t\t} else {\n\t\t\thead_anchor = set_hydrate_node(/** @type {TemplateNode} */ (get_next_sibling(head_anchor)));\n\t\t}\n\t}\n\n\tif (!hydrating) {\n\t\tanchor = document.head.appendChild(create_text());\n\t}\n\n\ttry {\n\t\tblock(() => render_fn(anchor), HEAD_EFFECT);\n\t} finally {\n\t\tif (was_hydrating) {\n\t\t\tset_hydrating(true);\n\t\t\thead_anchor = hydrate_node; // so that next head block starts from the correct node\n\t\t\tset_hydrate_node(/** @type {TemplateNode} */ (previous_hydrate_node));\n\t\t}\n\t}\n}\n", "/** @param {string} html */\nexport function create_fragment_from_html(html) {\n\tvar elem = document.createElement('template');\n\telem.innerHTML = html.replaceAll('<!>', '<!---->'); // XHTML compliance\n\treturn elem.content;\n}\n", "/** @import { Effect, TemplateNode } from '#client' */\n/** @import { TemplateStructure } from './types' */\nimport { hydrate_next, hydrate_node, hydrating, set_hydrate_node } from './hydration.js';\nimport {\n\tcreate_text,\n\tget_first_child,\n\tis_firefox,\n\tcreate_element,\n\tcreate_fragment,\n\tcreate_comment,\n\tset_attribute\n} from './operations.js';\nimport { create_fragment_from_html } from './reconciler.js';\nimport { active_effect } from '../runtime.js';\nimport {\n\tNAMESPACE_MATHML,\n\tNAMESPACE_SVG,\n\tTEMPLATE_FRAGMENT,\n\tTEMPLATE_USE_IMPORT_NODE,\n\tTEMPLATE_USE_MATHML,\n\tTEMPLATE_USE_SVG\n} from '../../../constants.js';\n\n/**\n * @param {TemplateNode} start\n * @param {TemplateNode | null} end\n */\nexport function assign_nodes(start, end) {\n\tvar effect = /** @type {Effect} */ (active_effect);\n\tif (effect.nodes_start === null) {\n\t\teffect.nodes_start = start;\n\t\teffect.nodes_end = end;\n\t}\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n * @returns {() => Node | Node[]}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function from_html(content, flags) {\n\tvar is_fragment = (flags & TEMPLATE_FRAGMENT) !== 0;\n\tvar use_import_node = (flags & TEMPLATE_USE_IMPORT_NODE) !== 0;\n\n\t/** @type {Node} */\n\tvar node;\n\n\t/**\n\t * Whether or not the first item is a text/element node. If not, we need to\n\t * create an additional comment node to act as `effect.nodes.start`\n\t */\n\tvar has_start = !content.startsWith('<!>');\n\n\treturn () => {\n\t\tif (hydrating) {\n\t\t\tassign_nodes(hydrate_node, null);\n\t\t\treturn hydrate_node;\n\t\t}\n\n\t\tif (node === undefined) {\n\t\t\tnode = create_fragment_from_html(has_start ? content : '<!>' + content);\n\t\t\tif (!is_fragment) node = /** @type {Node} */ (get_first_child(node));\n\t\t}\n\n\t\tvar clone = /** @type {TemplateNode} */ (\n\t\t\tuse_import_node || is_firefox ? document.importNode(node, true) : node.cloneNode(true)\n\t\t);\n\n\t\tif (is_fragment) {\n\t\t\tvar start = /** @type {TemplateNode} */ (get_first_child(clone));\n\t\t\tvar end = /** @type {TemplateNode} */ (clone.lastChild);\n\n\t\t\tassign_nodes(start, end);\n\t\t} else {\n\t\t\tassign_nodes(clone, clone);\n\t\t}\n\n\t\treturn clone;\n\t};\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n * @param {'svg' | 'math'} ns\n * @returns {() => Node | Node[]}\n */\n/*#__NO_SIDE_EFFECTS__*/\nfunction from_namespace(content, flags, ns = 'svg') {\n\t/**\n\t * Whether or not the first item is a text/element node. If not, we need to\n\t * create an additional comment node to act as `effect.nodes.start`\n\t */\n\tvar has_start = !content.startsWith('<!>');\n\n\tvar is_fragment = (flags & TEMPLATE_FRAGMENT) !== 0;\n\tvar wrapped = `<${ns}>${has_start ? content : '<!>' + content}</${ns}>`;\n\n\t/** @type {Element | DocumentFragment} */\n\tvar node;\n\n\treturn () => {\n\t\tif (hydrating) {\n\t\t\tassign_nodes(hydrate_node, null);\n\t\t\treturn hydrate_node;\n\t\t}\n\n\t\tif (!node) {\n\t\t\tvar fragment = /** @type {DocumentFragment} */ (create_fragment_from_html(wrapped));\n\t\t\tvar root = /** @type {Element} */ (get_first_child(fragment));\n\n\t\t\tif (is_fragment) {\n\t\t\t\tnode = document.createDocumentFragment();\n\t\t\t\twhile (get_first_child(root)) {\n\t\t\t\t\tnode.appendChild(/** @type {Node} */ (get_first_child(root)));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tnode = /** @type {Element} */ (get_first_child(root));\n\t\t\t}\n\t\t}\n\n\t\tvar clone = /** @type {TemplateNode} */ (node.cloneNode(true));\n\n\t\tif (is_fragment) {\n\t\t\tvar start = /** @type {TemplateNode} */ (get_first_child(clone));\n\t\t\tvar end = /** @type {TemplateNode} */ (clone.lastChild);\n\n\t\t\tassign_nodes(start, end);\n\t\t} else {\n\t\t\tassign_nodes(clone, clone);\n\t\t}\n\n\t\treturn clone;\n\t};\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function from_svg(content, flags) {\n\treturn from_namespace(content, flags, 'svg');\n}\n\n/**\n * @param {string} content\n * @param {number} flags\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function from_mathml(content, flags) {\n\treturn from_namespace(content, flags, 'math');\n}\n\n/**\n * @param {TemplateStructure[]} structure\n * @param {NAMESPACE_SVG | NAMESPACE_MATHML | undefined} [ns]\n */\nfunction fragment_from_tree(structure, ns) {\n\tvar fragment = create_fragment();\n\n\tfor (var item of structure) {\n\t\tif (typeof item === 'string') {\n\t\t\tfragment.append(create_text(item));\n\t\t\tcontinue;\n\t\t}\n\n\t\t// if `preserveComments === true`, comments are represented as `['// <data>']`\n\t\tif (item === undefined || item[0][0] === '/') {\n\t\t\tfragment.append(create_comment(item ? item[0].slice(3) : ''));\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst [name, attributes, ...children] = item;\n\n\t\tconst namespace = name === 'svg' ? NAMESPACE_SVG : name === 'math' ? NAMESPACE_MATHML : ns;\n\n\t\tvar element = create_element(name, namespace, attributes?.is);\n\n\t\tfor (var key in attributes) {\n\t\t\tset_attribute(element, key, attributes[key]);\n\t\t}\n\n\t\tif (children.length > 0) {\n\t\t\tvar target =\n\t\t\t\telement.tagName === 'TEMPLATE'\n\t\t\t\t\t? /** @type {HTMLTemplateElement} */ (element).content\n\t\t\t\t\t: element;\n\n\t\t\ttarget.append(\n\t\t\t\tfragment_from_tree(children, element.tagName === 'foreignObject' ? undefined : namespace)\n\t\t\t);\n\t\t}\n\n\t\tfragment.append(element);\n\t}\n\n\treturn fragment;\n}\n\n/**\n * @param {TemplateStructure[]} structure\n * @param {number} flags\n * @returns {() => Node | Node[]}\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function from_tree(structure, flags) {\n\tvar is_fragment = (flags & TEMPLATE_FRAGMENT) !== 0;\n\tvar use_import_node = (flags & TEMPLATE_USE_IMPORT_NODE) !== 0;\n\n\t/** @type {Node} */\n\tvar node;\n\n\treturn () => {\n\t\tif (hydrating) {\n\t\t\tassign_nodes(hydrate_node, null);\n\t\t\treturn hydrate_node;\n\t\t}\n\n\t\tif (node === undefined) {\n\t\t\tconst ns =\n\t\t\t\t(flags & TEMPLATE_USE_SVG) !== 0\n\t\t\t\t\t? NAMESPACE_SVG\n\t\t\t\t\t: (flags & TEMPLATE_USE_MATHML) !== 0\n\t\t\t\t\t\t? NAMESPACE_MATHML\n\t\t\t\t\t\t: undefined;\n\n\t\t\tnode = fragment_from_tree(structure, ns);\n\t\t\tif (!is_fragment) node = /** @type {Node} */ (get_first_child(node));\n\t\t}\n\n\t\tvar clone = /** @type {TemplateNode} */ (\n\t\t\tuse_import_node || is_firefox ? document.importNode(node, true) : node.cloneNode(true)\n\t\t);\n\n\t\tif (is_fragment) {\n\t\t\tvar start = /** @type {TemplateNode} */ (get_first_child(clone));\n\t\t\tvar end = /** @type {TemplateNode} */ (clone.lastChild);\n\n\t\t\tassign_nodes(start, end);\n\t\t} else {\n\t\t\tassign_nodes(clone, clone);\n\t\t}\n\n\t\treturn clone;\n\t};\n}\n\n/**\n * @param {() => Element | DocumentFragment} fn\n */\nexport function with_script(fn) {\n\treturn () => run_scripts(fn());\n}\n\n/**\n * Creating a document fragment from HTML that contains script tags will not execute\n * the scripts. We need to replace the script tags with new ones so that they are executed.\n * @param {Element | DocumentFragment} node\n * @returns {Node | Node[]}\n */\nfunction run_scripts(node) {\n\t// scripts were SSR'd, in which case they will run\n\tif (hydrating) return node;\n\n\tconst is_fragment = node.nodeType === 11;\n\tconst scripts =\n\t\t/** @type {HTMLElement} */ (node).tagName === 'SCRIPT'\n\t\t\t? [/** @type {HTMLScriptElement} */ (node)]\n\t\t\t: node.querySelectorAll('script');\n\tconst effect = /** @type {Effect} */ (active_effect);\n\n\tfor (const script of scripts) {\n\t\tconst clone = document.createElement('script');\n\t\tfor (var attribute of script.attributes) {\n\t\t\tclone.setAttribute(attribute.name, attribute.value);\n\t\t}\n\n\t\tclone.textContent = script.textContent;\n\n\t\t// The script has changed - if it's at the edges, the effect now points at dead nodes\n\t\tif (is_fragment ? node.firstChild === script : node === script) {\n\t\t\teffect.nodes_start = clone;\n\t\t}\n\t\tif (is_fragment ? node.lastChild === script : node === script) {\n\t\t\teffect.nodes_end = clone;\n\t\t}\n\n\t\tscript.replaceWith(clone);\n\t}\n\treturn node;\n}\n\n/**\n * Don't mark this as side-effect-free, hydration needs to walk all nodes\n * @param {any} value\n */\nexport function text(value = '') {\n\tif (!hydrating) {\n\t\tvar t = create_text(value + '');\n\t\tassign_nodes(t, t);\n\t\treturn t;\n\t}\n\n\tvar node = hydrate_node;\n\n\tif (node.nodeType !== 3) {\n\t\t// if an {expression} is empty during SSR, we need to insert an empty text node\n\t\tnode.before((node = create_text()));\n\t\tset_hydrate_node(node);\n\t}\n\n\tassign_nodes(node, node);\n\treturn node;\n}\n\nexport function comment() {\n\t// we're not delegating to `template` here for performance reasons\n\tif (hydrating) {\n\t\tassign_nodes(hydrate_node, null);\n\t\treturn hydrate_node;\n\t}\n\n\tvar frag = document.createDocumentFragment();\n\tvar start = document.createComment('');\n\tvar anchor = create_text();\n\tfrag.append(start, anchor);\n\n\tassign_nodes(start, anchor);\n\n\treturn frag;\n}\n\n/**\n * Assign the created (or in hydration mode, traversed) dom elements to the current block\n * and insert the elements into the dom (in client mode).\n * @param {Text | Comment | Element} anchor\n * @param {DocumentFragment | Element} dom\n */\nexport function append(anchor, dom) {\n\tif (hydrating) {\n\t\t/** @type {Effect} */ (active_effect).nodes_end = hydrate_node;\n\t\thydrate_next();\n\t\treturn;\n\t}\n\n\tif (anchor === null) {\n\t\t// edge case — void `<svelte:element>` with content\n\t\treturn;\n\t}\n\n\tanchor.before(/** @type {Node} */ (dom));\n}\n\n/**\n * Create (or hydrate) an unique UID for the component instance.\n */\nexport function props_id() {\n\tif (\n\t\thydrating &&\n\t\thydrate_node &&\n\t\thydrate_node.nodeType === 8 &&\n\t\thydrate_node.textContent?.startsWith(`#`)\n\t) {\n\t\tconst id = hydrate_node.textContent.substring(1);\n\t\thydrate_next();\n\t\treturn id;\n\t}\n\n\t// @ts-expect-error This way we ensure the id is unique even across Svelte runtimes\n\t(window.__svelte ??= {}).uid ??= 1;\n\n\t// @ts-expect-error\n\treturn `c${window.__svelte.uid++}`;\n}\n", "/** @import { ComponentContext, Effect, TemplateNode } from '#client' */\n/** @import { Component, ComponentType, SvelteComponent, MountOptions } from '../../index.js' */\nimport { DEV } from 'esm-env';\nimport {\n\tclear_text_content,\n\tcreate_text,\n\tget_first_child,\n\tget_next_sibling,\n\tinit_operations\n} from './dom/operations.js';\nimport { HYDRATION_END, HYDRATION_ERROR, HYDRATION_START } from '../../constants.js';\nimport { active_effect } from './runtime.js';\nimport { push, pop, component_context } from './context.js';\nimport { component_root, branch } from './reactivity/effects.js';\nimport {\n\thydrate_next,\n\thydrate_node,\n\thydrating,\n\tset_hydrate_node,\n\tset_hydrating\n} from './dom/hydration.js';\nimport { array_from } from '../shared/utils.js';\nimport {\n\tall_registered_events,\n\thandle_event_propagation,\n\troot_event_handles\n} from './dom/elements/events.js';\nimport { reset_head_anchor } from './dom/blocks/svelte-head.js';\nimport * as w from './warnings.js';\nimport * as e from './errors.js';\nimport { assign_nodes } from './dom/template.js';\nimport { is_passive_event } from '../../utils.js';\n\n/**\n * This is normally true — block effects should run their intro transitions —\n * but is false during hydration (unless `options.intro` is `true`) and\n * when creating the children of a `<svelte:element>` that just changed tag\n */\nexport let should_intro = true;\n\n/** @param {boolean} value */\nexport function set_should_intro(value) {\n\tshould_intro = value;\n}\n\n/**\n * @param {Element} text\n * @param {string} value\n * @returns {void}\n */\nexport function set_text(text, value) {\n\t// For objects, we apply string coercion (which might make things like $state array references in the template reactive) before diffing\n\tvar str = value == null ? '' : typeof value === 'object' ? value + '' : value;\n\t// @ts-expect-error\n\tif (str !== (text.__t ??= text.nodeValue)) {\n\t\t// @ts-expect-error\n\t\ttext.__t = str;\n\t\ttext.nodeValue = str + '';\n\t}\n}\n\n/**\n * Mounts a component to the given target and returns the exports and potentially the props (if compiled with `accessors: true`) of the component.\n * Transitions will play during the initial render unless the `intro` option is set to `false`.\n *\n * @template {Record<string, any>} Props\n * @template {Record<string, any>} Exports\n * @param {ComponentType<SvelteComponent<Props>> | Component<Props, Exports, any>} component\n * @param {MountOptions<Props>} options\n * @returns {Exports}\n */\nexport function mount(component, options) {\n\treturn _mount(component, options);\n}\n\n/**\n * Hydrates a component on the given target and returns the exports and potentially the props (if compiled with `accessors: true`) of the component\n *\n * @template {Record<string, any>} Props\n * @template {Record<string, any>} Exports\n * @param {ComponentType<SvelteComponent<Props>> | Component<Props, Exports, any>} component\n * @param {{} extends Props ? {\n * \t\ttarget: Document | Element | ShadowRoot;\n * \t\tprops?: Props;\n * \t\tevents?: Record<string, (e: any) => any>;\n *  \tcontext?: Map<any, any>;\n * \t\tintro?: boolean;\n * \t\trecover?: boolean;\n * \t} : {\n * \t\ttarget: Document | Element | ShadowRoot;\n * \t\tprops: Props;\n * \t\tevents?: Record<string, (e: any) => any>;\n *  \tcontext?: Map<any, any>;\n * \t\tintro?: boolean;\n * \t\trecover?: boolean;\n * \t}} options\n * @returns {Exports}\n */\nexport function hydrate(component, options) {\n\tinit_operations();\n\toptions.intro = options.intro ?? false;\n\tconst target = options.target;\n\tconst was_hydrating = hydrating;\n\tconst previous_hydrate_node = hydrate_node;\n\n\ttry {\n\t\tvar anchor = /** @type {TemplateNode} */ (get_first_child(target));\n\t\twhile (\n\t\t\tanchor &&\n\t\t\t(anchor.nodeType !== 8 || /** @type {Comment} */ (anchor).data !== HYDRATION_START)\n\t\t) {\n\t\t\tanchor = /** @type {TemplateNode} */ (get_next_sibling(anchor));\n\t\t}\n\n\t\tif (!anchor) {\n\t\t\tthrow HYDRATION_ERROR;\n\t\t}\n\n\t\tset_hydrating(true);\n\t\tset_hydrate_node(/** @type {Comment} */ (anchor));\n\t\thydrate_next();\n\n\t\tconst instance = _mount(component, { ...options, anchor });\n\n\t\tif (\n\t\t\thydrate_node === null ||\n\t\t\thydrate_node.nodeType !== 8 ||\n\t\t\t/** @type {Comment} */ (hydrate_node).data !== HYDRATION_END\n\t\t) {\n\t\t\tw.hydration_mismatch();\n\t\t\tthrow HYDRATION_ERROR;\n\t\t}\n\n\t\tset_hydrating(false);\n\n\t\treturn /**  @type {Exports} */ (instance);\n\t} catch (error) {\n\t\tif (error === HYDRATION_ERROR) {\n\t\t\tif (options.recover === false) {\n\t\t\t\te.hydration_failed();\n\t\t\t}\n\n\t\t\t// If an error occured above, the operations might not yet have been initialised.\n\t\t\tinit_operations();\n\t\t\tclear_text_content(target);\n\n\t\t\tset_hydrating(false);\n\t\t\treturn mount(component, options);\n\t\t}\n\n\t\tthrow error;\n\t} finally {\n\t\tset_hydrating(was_hydrating);\n\t\tset_hydrate_node(previous_hydrate_node);\n\t\treset_head_anchor();\n\t}\n}\n\n/** @type {Map<string, number>} */\nconst document_listeners = new Map();\n\n/**\n * @template {Record<string, any>} Exports\n * @param {ComponentType<SvelteComponent<any>> | Component<any>} Component\n * @param {MountOptions} options\n * @returns {Exports}\n */\nfunction _mount(Component, { target, anchor, props = {}, events, context, intro = true }) {\n\tinit_operations();\n\n\tvar registered_events = new Set();\n\n\t/** @param {Array<string>} events */\n\tvar event_handle = (events) => {\n\t\tfor (var i = 0; i < events.length; i++) {\n\t\t\tvar event_name = events[i];\n\n\t\t\tif (registered_events.has(event_name)) continue;\n\t\t\tregistered_events.add(event_name);\n\n\t\t\tvar passive = is_passive_event(event_name);\n\n\t\t\t// Add the event listener to both the container and the document.\n\t\t\t// The container listener ensures we catch events from within in case\n\t\t\t// the outer content stops propagation of the event.\n\t\t\ttarget.addEventListener(event_name, handle_event_propagation, { passive });\n\n\t\t\tvar n = document_listeners.get(event_name);\n\n\t\t\tif (n === undefined) {\n\t\t\t\t// The document listener ensures we catch events that originate from elements that were\n\t\t\t\t// manually moved outside of the container (e.g. via manual portals).\n\t\t\t\tdocument.addEventListener(event_name, handle_event_propagation, { passive });\n\t\t\t\tdocument_listeners.set(event_name, 1);\n\t\t\t} else {\n\t\t\t\tdocument_listeners.set(event_name, n + 1);\n\t\t\t}\n\t\t}\n\t};\n\n\tevent_handle(array_from(all_registered_events));\n\troot_event_handles.add(event_handle);\n\n\t/** @type {Exports} */\n\t// @ts-expect-error will be defined because the render effect runs synchronously\n\tvar component = undefined;\n\n\tvar unmount = component_root(() => {\n\t\tvar anchor_node = anchor ?? target.appendChild(create_text());\n\n\t\tbranch(() => {\n\t\t\tif (context) {\n\t\t\t\tpush({});\n\t\t\t\tvar ctx = /** @type {ComponentContext} */ (component_context);\n\t\t\t\tctx.c = context;\n\t\t\t}\n\n\t\t\tif (events) {\n\t\t\t\t// We can't spread the object or else we'd lose the state proxy stuff, if it is one\n\t\t\t\t/** @type {any} */ (props).$$events = events;\n\t\t\t}\n\n\t\t\tif (hydrating) {\n\t\t\t\tassign_nodes(/** @type {TemplateNode} */ (anchor_node), null);\n\t\t\t}\n\n\t\t\tshould_intro = intro;\n\t\t\t// @ts-expect-error the public typings are not what the actual function looks like\n\t\t\tcomponent = Component(anchor_node, props) || {};\n\t\t\tshould_intro = true;\n\n\t\t\tif (hydrating) {\n\t\t\t\t/** @type {Effect} */ (active_effect).nodes_end = hydrate_node;\n\t\t\t}\n\n\t\t\tif (context) {\n\t\t\t\tpop();\n\t\t\t}\n\t\t});\n\n\t\treturn () => {\n\t\t\tfor (var event_name of registered_events) {\n\t\t\t\ttarget.removeEventListener(event_name, handle_event_propagation);\n\n\t\t\t\tvar n = /** @type {number} */ (document_listeners.get(event_name));\n\n\t\t\t\tif (--n === 0) {\n\t\t\t\t\tdocument.removeEventListener(event_name, handle_event_propagation);\n\t\t\t\t\tdocument_listeners.delete(event_name);\n\t\t\t\t} else {\n\t\t\t\t\tdocument_listeners.set(event_name, n);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\troot_event_handles.delete(event_handle);\n\n\t\t\tif (anchor_node !== anchor) {\n\t\t\t\tanchor_node.parentNode?.removeChild(anchor_node);\n\t\t\t}\n\t\t};\n\t});\n\n\tmounted_components.set(component, unmount);\n\treturn component;\n}\n\n/**\n * References of the components that were mounted or hydrated.\n * Uses a `WeakMap` to avoid memory leaks.\n */\nlet mounted_components = new WeakMap();\n\n/**\n * Unmounts a component that was previously mounted using `mount` or `hydrate`.\n *\n * Since 5.13.0, if `options.outro` is `true`, [transitions](https://svelte.dev/docs/svelte/transition) will play before the component is removed from the DOM.\n *\n * Returns a `Promise` that resolves after transitions have completed if `options.outro` is true, or immediately otherwise (prior to 5.13.0, returns `void`).\n *\n * ```js\n * import { mount, unmount } from 'svelte';\n * import App from './App.svelte';\n *\n * const app = mount(App, { target: document.body });\n *\n * // later...\n * unmount(app, { outro: true });\n * ```\n * @param {Record<string, any>} component\n * @param {{ outro?: boolean }} [options]\n * @returns {Promise<void>}\n */\nexport function unmount(component, options) {\n\tconst fn = mounted_components.get(component);\n\n\tif (fn) {\n\t\tmounted_components.delete(component);\n\t\treturn fn(options);\n\t}\n\n\tif (DEV) {\n\t\tw.lifecycle_double_unmount();\n\t}\n\n\treturn Promise.resolve();\n}\n", "/** @import { Snippet } from 'svelte' */\n/** @import { Effect, TemplateNode } from '#client' */\n/** @import { Getters } from '#shared' */\nimport { EFFECT_TRANSPARENT } from '#client/constants';\nimport { branch, block, destroy_effect, teardown } from '../../reactivity/effects.js';\nimport {\n\tdev_current_component_function,\n\tset_dev_current_component_function\n} from '../../context.js';\nimport { hydrate_next, hydrate_node, hydrating } from '../hydration.js';\nimport { create_fragment_from_html } from '../reconciler.js';\nimport { assign_nodes } from '../template.js';\nimport * as w from '../../warnings.js';\nimport * as e from '../../errors.js';\nimport { DEV } from 'esm-env';\nimport { get_first_child, get_next_sibling } from '../operations.js';\nimport { noop } from '../../../shared/utils.js';\nimport { prevent_snippet_stringification } from '../../../shared/validate.js';\n\n/**\n * @template {(node: TemplateNode, ...args: any[]) => void} SnippetFn\n * @param {TemplateNode} node\n * @param {() => SnippetFn | null | undefined} get_snippet\n * @param {(() => any)[]} args\n * @returns {void}\n */\nexport function snippet(node, get_snippet, ...args) {\n\tvar anchor = node;\n\n\t/** @type {SnippetFn | null | undefined} */\n\t// @ts-ignore\n\tvar snippet = noop;\n\n\t/** @type {Effect | null} */\n\tvar snippet_effect;\n\n\tblock(() => {\n\t\tif (snippet === (snippet = get_snippet())) return;\n\n\t\tif (snippet_effect) {\n\t\t\tdestroy_effect(snippet_effect);\n\t\t\tsnippet_effect = null;\n\t\t}\n\n\t\tif (DEV && snippet == null) {\n\t\t\te.invalid_snippet();\n\t\t}\n\n\t\tsnippet_effect = branch(() => /** @type {SnippetFn} */ (snippet)(anchor, ...args));\n\t}, EFFECT_TRANSPARENT);\n\n\tif (hydrating) {\n\t\tanchor = hydrate_node;\n\t}\n}\n\n/**\n * In development, wrap the snippet function so that it passes validation, and so that the\n * correct component context is set for ownership checks\n * @param {any} component\n * @param {(node: TemplateNode, ...args: any[]) => void} fn\n */\nexport function wrap_snippet(component, fn) {\n\tconst snippet = (/** @type {TemplateNode} */ node, /** @type {any[]} */ ...args) => {\n\t\tvar previous_component_function = dev_current_component_function;\n\t\tset_dev_current_component_function(component);\n\n\t\ttry {\n\t\t\treturn fn(node, ...args);\n\t\t} finally {\n\t\t\tset_dev_current_component_function(previous_component_function);\n\t\t}\n\t};\n\n\tprevent_snippet_stringification(snippet);\n\n\treturn snippet;\n}\n\n/**\n * Create a snippet programmatically\n * @template {unknown[]} Params\n * @param {(...params: Getters<Params>) => {\n *   render: () => string\n *   setup?: (element: Element) => void | (() => void)\n * }} fn\n * @returns {Snippet<Params>}\n */\nexport function createRawSnippet(fn) {\n\t// @ts-expect-error the types are a lie\n\treturn (/** @type {TemplateNode} */ anchor, /** @type {Getters<Params>} */ ...params) => {\n\t\tvar snippet = fn(...params);\n\n\t\t/** @type {Element} */\n\t\tvar element;\n\n\t\tif (hydrating) {\n\t\t\telement = /** @type {Element} */ (hydrate_node);\n\t\t\thydrate_next();\n\t\t} else {\n\t\t\tvar html = snippet.render().trim();\n\t\t\tvar fragment = create_fragment_from_html(html);\n\t\t\telement = /** @type {Element} */ (get_first_child(fragment));\n\n\t\t\tif (DEV && (get_next_sibling(element) !== null || element.nodeType !== 1)) {\n\t\t\t\tw.invalid_raw_snippet_render();\n\t\t\t}\n\n\t\t\tanchor.before(element);\n\t\t}\n\n\t\tconst result = snippet.setup?.(element);\n\t\tassign_nodes(element, element);\n\n\t\tif (typeof result === 'function') {\n\t\t\tteardown(result);\n\t\t}\n\t};\n}\n", "import { escape_html } from '../../escaping.js';\nimport { clsx as _clsx } from 'clsx';\n\n/**\n * `<div translate={false}>` should be rendered as `<div translate=\"no\">` and _not_\n * `<div translate=\"false\">`, which is equivalent to `<div translate=\"yes\">`. There\n * may be other odd cases that need to be added to this list in future\n * @type {Record<string, Map<any, string>>}\n */\nconst replacements = {\n\ttranslate: new Map([\n\t\t[true, 'yes'],\n\t\t[false, 'no']\n\t])\n};\n\n/**\n * @template V\n * @param {string} name\n * @param {V} value\n * @param {boolean} [is_boolean]\n * @returns {string}\n */\nexport function attr(name, value, is_boolean = false) {\n\tif (value == null || (!value && is_boolean)) return '';\n\tconst normalized = (name in replacements && replacements[name].get(value)) || value;\n\tconst assignment = is_boolean ? '' : `=\"${escape_html(normalized, true)}\"`;\n\treturn ` ${name}${assignment}`;\n}\n\n/**\n * Small wrapper around clsx to preserve Svelte's (weird) handling of falsy values.\n * TODO Svelte 6 revisit this, and likely turn all falsy values into the empty string (what clsx also does)\n * @param  {any} value\n */\nexport function clsx(value) {\n\tif (typeof value === 'object') {\n\t\treturn _clsx(value);\n\t} else {\n\t\treturn value ?? '';\n\t}\n}\n\nconst whitespace = [...' \\t\\n\\r\\f\\u00a0\\u000b\\ufeff'];\n\n/**\n * @param {any} value\n * @param {string | null} [hash]\n * @param {Record<string, boolean>} [directives]\n * @returns {string | null}\n */\nexport function to_class(value, hash, directives) {\n\tvar classname = value == null ? '' : '' + value;\n\n\tif (hash) {\n\t\tclassname = classname ? classname + ' ' + hash : hash;\n\t}\n\n\tif (directives) {\n\t\tfor (var key in directives) {\n\t\t\tif (directives[key]) {\n\t\t\t\tclassname = classname ? classname + ' ' + key : key;\n\t\t\t} else if (classname.length) {\n\t\t\t\tvar len = key.length;\n\t\t\t\tvar a = 0;\n\n\t\t\t\twhile ((a = classname.indexOf(key, a)) >= 0) {\n\t\t\t\t\tvar b = a + len;\n\n\t\t\t\t\tif (\n\t\t\t\t\t\t(a === 0 || whitespace.includes(classname[a - 1])) &&\n\t\t\t\t\t\t(b === classname.length || whitespace.includes(classname[b]))\n\t\t\t\t\t) {\n\t\t\t\t\t\tclassname = (a === 0 ? '' : classname.substring(0, a)) + classname.substring(b + 1);\n\t\t\t\t\t} else {\n\t\t\t\t\t\ta = b;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn classname === '' ? null : classname;\n}\n\n/**\n *\n * @param {Record<string,any>} styles\n * @param {boolean} important\n */\nfunction append_styles(styles, important = false) {\n\tvar separator = important ? ' !important;' : ';';\n\tvar css = '';\n\n\tfor (var key in styles) {\n\t\tvar value = styles[key];\n\t\tif (value != null && value !== '') {\n\t\t\tcss += ' ' + key + ': ' + value + separator;\n\t\t}\n\t}\n\n\treturn css;\n}\n\n/**\n * @param {string} name\n * @returns {string}\n */\nfunction to_css_name(name) {\n\tif (name[0] !== '-' || name[1] !== '-') {\n\t\treturn name.toLowerCase();\n\t}\n\treturn name;\n}\n\n/**\n * @param {any} value\n * @param {Record<string, any> | [Record<string, any>, Record<string, any>]} [styles]\n * @returns {string | null}\n */\nexport function to_style(value, styles) {\n\tif (styles) {\n\t\tvar new_style = '';\n\n\t\t/** @type {Record<string,any> | undefined} */\n\t\tvar normal_styles;\n\n\t\t/** @type {Record<string,any> | undefined} */\n\t\tvar important_styles;\n\n\t\tif (Array.isArray(styles)) {\n\t\t\tnormal_styles = styles[0];\n\t\t\timportant_styles = styles[1];\n\t\t} else {\n\t\t\tnormal_styles = styles;\n\t\t}\n\n\t\tif (value) {\n\t\t\tvalue = String(value)\n\t\t\t\t.replaceAll(/\\s*\\/\\*.*?\\*\\/\\s*/g, '')\n\t\t\t\t.trim();\n\n\t\t\t/** @type {boolean | '\"' | \"'\"} */\n\t\t\tvar in_str = false;\n\t\t\tvar in_apo = 0;\n\t\t\tvar in_comment = false;\n\n\t\t\tvar reserved_names = [];\n\n\t\t\tif (normal_styles) {\n\t\t\t\treserved_names.push(...Object.keys(normal_styles).map(to_css_name));\n\t\t\t}\n\t\t\tif (important_styles) {\n\t\t\t\treserved_names.push(...Object.keys(important_styles).map(to_css_name));\n\t\t\t}\n\n\t\t\tvar start_index = 0;\n\t\t\tvar name_index = -1;\n\n\t\t\tconst len = value.length;\n\t\t\tfor (var i = 0; i < len; i++) {\n\t\t\t\tvar c = value[i];\n\n\t\t\t\tif (in_comment) {\n\t\t\t\t\tif (c === '/' && value[i - 1] === '*') {\n\t\t\t\t\t\tin_comment = false;\n\t\t\t\t\t}\n\t\t\t\t} else if (in_str) {\n\t\t\t\t\tif (in_str === c) {\n\t\t\t\t\t\tin_str = false;\n\t\t\t\t\t}\n\t\t\t\t} else if (c === '/' && value[i + 1] === '*') {\n\t\t\t\t\tin_comment = true;\n\t\t\t\t} else if (c === '\"' || c === \"'\") {\n\t\t\t\t\tin_str = c;\n\t\t\t\t} else if (c === '(') {\n\t\t\t\t\tin_apo++;\n\t\t\t\t} else if (c === ')') {\n\t\t\t\t\tin_apo--;\n\t\t\t\t}\n\n\t\t\t\tif (!in_comment && in_str === false && in_apo === 0) {\n\t\t\t\t\tif (c === ':' && name_index === -1) {\n\t\t\t\t\t\tname_index = i;\n\t\t\t\t\t} else if (c === ';' || i === len - 1) {\n\t\t\t\t\t\tif (name_index !== -1) {\n\t\t\t\t\t\t\tvar name = to_css_name(value.substring(start_index, name_index).trim());\n\n\t\t\t\t\t\t\tif (!reserved_names.includes(name)) {\n\t\t\t\t\t\t\t\tif (c !== ';') {\n\t\t\t\t\t\t\t\t\ti++;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tvar property = value.substring(start_index, i).trim();\n\t\t\t\t\t\t\t\tnew_style += ' ' + property + ';';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tstart_index = i + 1;\n\t\t\t\t\t\tname_index = -1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (normal_styles) {\n\t\t\tnew_style += append_styles(normal_styles);\n\t\t}\n\n\t\tif (important_styles) {\n\t\t\tnew_style += append_styles(important_styles, true);\n\t\t}\n\n\t\tnew_style = new_style.trim();\n\t\treturn new_style === '' ? null : new_style;\n\t}\n\n\treturn value == null ? null : String(value);\n}\n", "/** @import { Effect } from '#client' */\nimport { DEV } from 'esm-env';\nimport { hydrating, set_hydrating } from '../hydration.js';\nimport { get_descriptors, get_prototype_of } from '../../../shared/utils.js';\nimport { create_event, delegate } from './events.js';\nimport { add_form_reset_listener, autofocus } from './misc.js';\nimport * as w from '../../warnings.js';\nimport { LOADING_ATTR_SYMBOL } from '#client/constants';\nimport { queue_idle_task } from '../task.js';\nimport { is_capture_event, is_delegated, normalize_attribute } from '../../../../utils.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tget,\n\tset_active_effect,\n\tset_active_reaction\n} from '../../runtime.js';\nimport { attach } from './attachments.js';\nimport { clsx } from '../../../shared/attributes.js';\nimport { set_class } from './class.js';\nimport { set_style } from './style.js';\nimport { ATTACHMENT_KEY, NAMESPACE_HTML } from '../../../../constants.js';\nimport { block, branch, destroy_effect } from '../../reactivity/effects.js';\nimport { derived } from '../../reactivity/deriveds.js';\nimport { init_select, select_option } from './bindings/select.js';\n\nexport const CLASS = Symbol('class');\nexport const STYLE = Symbol('style');\n\nconst IS_CUSTOM_ELEMENT = Symbol('is custom element');\nconst IS_HTML = Symbol('is html');\n\n/**\n * The value/checked attribute in the template actually corresponds to the defaultValue property, so we need\n * to remove it upon hydration to avoid a bug when someone resets the form value.\n * @param {HTMLInputElement} input\n * @returns {void}\n */\nexport function remove_input_defaults(input) {\n\tif (!hydrating) return;\n\n\tvar already_removed = false;\n\n\t// We try and remove the default attributes later, rather than sync during hydration.\n\t// Doing it sync during hydration has a negative impact on performance, but deferring the\n\t// work in an idle task alleviates this greatly. If a form reset event comes in before\n\t// the idle callback, then we ensure the input defaults are cleared just before.\n\tvar remove_defaults = () => {\n\t\tif (already_removed) return;\n\t\talready_removed = true;\n\n\t\t// Remove the attributes but preserve the values\n\t\tif (input.hasAttribute('value')) {\n\t\t\tvar value = input.value;\n\t\t\tset_attribute(input, 'value', null);\n\t\t\tinput.value = value;\n\t\t}\n\n\t\tif (input.hasAttribute('checked')) {\n\t\t\tvar checked = input.checked;\n\t\t\tset_attribute(input, 'checked', null);\n\t\t\tinput.checked = checked;\n\t\t}\n\t};\n\n\t// @ts-expect-error\n\tinput.__on_r = remove_defaults;\n\tqueue_idle_task(remove_defaults);\n\tadd_form_reset_listener();\n}\n\n/**\n * @param {Element} element\n * @param {any} value\n */\nexport function set_value(element, value) {\n\tvar attributes = get_attributes(element);\n\n\tif (\n\t\tattributes.value ===\n\t\t\t(attributes.value =\n\t\t\t\t// treat null and undefined the same for the initial value\n\t\t\t\tvalue ?? undefined) ||\n\t\t// @ts-expect-error\n\t\t// `progress` elements always need their value set when it's `0`\n\t\t(element.value === value && (value !== 0 || element.nodeName !== 'PROGRESS'))\n\t) {\n\t\treturn;\n\t}\n\n\t// @ts-expect-error\n\telement.value = value ?? '';\n}\n\n/**\n * @param {Element} element\n * @param {boolean} checked\n */\nexport function set_checked(element, checked) {\n\tvar attributes = get_attributes(element);\n\n\tif (\n\t\tattributes.checked ===\n\t\t(attributes.checked =\n\t\t\t// treat null and undefined the same for the initial value\n\t\t\tchecked ?? undefined)\n\t) {\n\t\treturn;\n\t}\n\n\t// @ts-expect-error\n\telement.checked = checked;\n}\n\n/**\n * Sets the `selected` attribute on an `option` element.\n * Not set through the property because that doesn't reflect to the DOM,\n * which means it wouldn't be taken into account when a form is reset.\n * @param {HTMLOptionElement} element\n * @param {boolean} selected\n */\nexport function set_selected(element, selected) {\n\tif (selected) {\n\t\t// The selected option could've changed via user selection, and\n\t\t// setting the value without this check would set it back.\n\t\tif (!element.hasAttribute('selected')) {\n\t\t\telement.setAttribute('selected', '');\n\t\t}\n\t} else {\n\t\telement.removeAttribute('selected');\n\t}\n}\n\n/**\n * Applies the default checked property without influencing the current checked property.\n * @param {HTMLInputElement} element\n * @param {boolean} checked\n */\nexport function set_default_checked(element, checked) {\n\tconst existing_value = element.checked;\n\telement.defaultChecked = checked;\n\telement.checked = existing_value;\n}\n\n/**\n * Applies the default value property without influencing the current value property.\n * @param {HTMLInputElement | HTMLTextAreaElement} element\n * @param {string} value\n */\nexport function set_default_value(element, value) {\n\tconst existing_value = element.value;\n\telement.defaultValue = value;\n\telement.value = existing_value;\n}\n\n/**\n * @param {Element} element\n * @param {string} attribute\n * @param {string | null} value\n * @param {boolean} [skip_warning]\n */\nexport function set_attribute(element, attribute, value, skip_warning) {\n\tvar attributes = get_attributes(element);\n\n\tif (hydrating) {\n\t\tattributes[attribute] = element.getAttribute(attribute);\n\n\t\tif (\n\t\t\tattribute === 'src' ||\n\t\t\tattribute === 'srcset' ||\n\t\t\t(attribute === 'href' && element.nodeName === 'LINK')\n\t\t) {\n\t\t\tif (!skip_warning) {\n\t\t\t\tcheck_src_in_dev_hydration(element, attribute, value ?? '');\n\t\t\t}\n\n\t\t\t// If we reset these attributes, they would result in another network request, which we want to avoid.\n\t\t\t// We assume they are the same between client and server as checking if they are equal is expensive\n\t\t\t// (we can't just compare the strings as they can be different between client and server but result in the\n\t\t\t// same url, so we would need to create hidden anchor elements to compare them)\n\t\t\treturn;\n\t\t}\n\t}\n\n\tif (attributes[attribute] === (attributes[attribute] = value)) return;\n\n\tif (attribute === 'loading') {\n\t\t// @ts-expect-error\n\t\telement[LOADING_ATTR_SYMBOL] = value;\n\t}\n\n\tif (value == null) {\n\t\telement.removeAttribute(attribute);\n\t} else if (typeof value !== 'string' && get_setters(element).includes(attribute)) {\n\t\t// @ts-ignore\n\t\telement[attribute] = value;\n\t} else {\n\t\telement.setAttribute(attribute, value);\n\t}\n}\n\n/**\n * @param {Element} dom\n * @param {string} attribute\n * @param {string} value\n */\nexport function set_xlink_attribute(dom, attribute, value) {\n\tdom.setAttributeNS('http://www.w3.org/1999/xlink', attribute, value);\n}\n\n/**\n * @param {HTMLElement} node\n * @param {string} prop\n * @param {any} value\n */\nexport function set_custom_element_data(node, prop, value) {\n\t// We need to ensure that setting custom element props, which can\n\t// invoke lifecycle methods on other custom elements, does not also\n\t// associate those lifecycle methods with the current active reaction\n\t// or effect\n\tvar previous_reaction = active_reaction;\n\tvar previous_effect = active_effect;\n\n\t// If we're hydrating but the custom element is from Svelte, and it already scaffolded,\n\t// then it might run block logic in hydration mode, which we have to prevent.\n\tlet was_hydrating = hydrating;\n\tif (hydrating) {\n\t\tset_hydrating(false);\n\t}\n\n\tset_active_reaction(null);\n\tset_active_effect(null);\n\n\ttry {\n\t\tif (\n\t\t\t// `style` should use `set_attribute` rather than the setter\n\t\t\tprop !== 'style' &&\n\t\t\t// Don't compute setters for custom elements while they aren't registered yet,\n\t\t\t// because during their upgrade/instantiation they might add more setters.\n\t\t\t// Instead, fall back to a simple \"an object, then set as property\" heuristic.\n\t\t\t(setters_cache.has(node.nodeName) ||\n\t\t\t// customElements may not be available in browser extension contexts\n\t\t\t!customElements ||\n\t\t\tcustomElements.get(node.tagName.toLowerCase())\n\t\t\t\t? get_setters(node).includes(prop)\n\t\t\t\t: value && typeof value === 'object')\n\t\t) {\n\t\t\t// @ts-expect-error\n\t\t\tnode[prop] = value;\n\t\t} else {\n\t\t\t// We did getters etc checks already, stringify before passing to set_attribute\n\t\t\t// to ensure it doesn't invoke the same logic again, and potentially populating\n\t\t\t// the setters cache too early.\n\t\t\tset_attribute(node, prop, value == null ? value : String(value));\n\t\t}\n\t} finally {\n\t\tset_active_reaction(previous_reaction);\n\t\tset_active_effect(previous_effect);\n\t\tif (was_hydrating) {\n\t\t\tset_hydrating(true);\n\t\t}\n\t}\n}\n\n/**\n * Spreads attributes onto a DOM element, taking into account the currently set attributes\n * @param {Element & ElementCSSInlineStyle} element\n * @param {Record<string | symbol, any> | undefined} prev\n * @param {Record<string | symbol, any>} next New attributes - this function mutates this object\n * @param {string} [css_hash]\n * @param {boolean} [skip_warning]\n * @returns {Record<string, any>}\n */\nexport function set_attributes(element, prev, next, css_hash, skip_warning = false) {\n\tvar attributes = get_attributes(element);\n\n\tvar is_custom_element = attributes[IS_CUSTOM_ELEMENT];\n\tvar preserve_attribute_case = !attributes[IS_HTML];\n\n\t// If we're hydrating but the custom element is from Svelte, and it already scaffolded,\n\t// then it might run block logic in hydration mode, which we have to prevent.\n\tlet is_hydrating_custom_element = hydrating && is_custom_element;\n\tif (is_hydrating_custom_element) {\n\t\tset_hydrating(false);\n\t}\n\n\tvar current = prev || {};\n\tvar is_option_element = element.tagName === 'OPTION';\n\n\tfor (var key in prev) {\n\t\tif (!(key in next)) {\n\t\t\tnext[key] = null;\n\t\t}\n\t}\n\n\tif (next.class) {\n\t\tnext.class = clsx(next.class);\n\t} else if (css_hash || next[CLASS]) {\n\t\tnext.class = null; /* force call to set_class() */\n\t}\n\n\tif (next[STYLE]) {\n\t\tnext.style ??= null; /* force call to set_style() */\n\t}\n\n\tvar setters = get_setters(element);\n\n\t// since key is captured we use const\n\tfor (const key in next) {\n\t\t// let instead of var because referenced in a closure\n\t\tlet value = next[key];\n\n\t\t// Up here because we want to do this for the initial value, too, even if it's undefined,\n\t\t// and this wouldn't be reached in case of undefined because of the equality check below\n\t\tif (is_option_element && key === 'value' && value == null) {\n\t\t\t// The <option> element is a special case because removing the value attribute means\n\t\t\t// the value is set to the text content of the option element, and setting the value\n\t\t\t// to null or undefined means the value is set to the string \"null\" or \"undefined\".\n\t\t\t// To align with how we handle this case in non-spread-scenarios, this logic is needed.\n\t\t\t// There's a super-edge-case bug here that is left in in favor of smaller code size:\n\t\t\t// Because of the \"set missing props to null\" logic above, we can't differentiate\n\t\t\t// between a missing value and an explicitly set value of null or undefined. That means\n\t\t\t// that once set, the value attribute of an <option> element can't be removed. This is\n\t\t\t// a very rare edge case, and removing the attribute altogether isn't possible either\n\t\t\t// for the <option value={undefined}> case, so we're not losing any functionality here.\n\t\t\t// @ts-ignore\n\t\t\telement.value = element.__value = '';\n\t\t\tcurrent[key] = value;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (key === 'class') {\n\t\t\tvar is_html = element.namespaceURI === 'http://www.w3.org/1999/xhtml';\n\t\t\tset_class(element, is_html, value, css_hash, prev?.[CLASS], next[CLASS]);\n\t\t\tcurrent[key] = value;\n\t\t\tcurrent[CLASS] = next[CLASS];\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (key === 'style') {\n\t\t\tset_style(element, value, prev?.[STYLE], next[STYLE]);\n\t\t\tcurrent[key] = value;\n\t\t\tcurrent[STYLE] = next[STYLE];\n\t\t\tcontinue;\n\t\t}\n\n\t\tvar prev_value = current[key];\n\t\tif (value === prev_value) continue;\n\n\t\tcurrent[key] = value;\n\n\t\tvar prefix = key[0] + key[1]; // this is faster than key.slice(0, 2)\n\t\tif (prefix === '$$') continue;\n\n\t\tif (prefix === 'on') {\n\t\t\t/** @type {{ capture?: true }} */\n\t\t\tconst opts = {};\n\t\t\tconst event_handle_key = '$$' + key;\n\t\t\tlet event_name = key.slice(2);\n\t\t\tvar delegated = is_delegated(event_name);\n\n\t\t\tif (is_capture_event(event_name)) {\n\t\t\t\tevent_name = event_name.slice(0, -7);\n\t\t\t\topts.capture = true;\n\t\t\t}\n\n\t\t\tif (!delegated && prev_value) {\n\t\t\t\t// Listening to same event but different handler -> our handle function below takes care of this\n\t\t\t\t// If we were to remove and add listeners in this case, it could happen that the event is \"swallowed\"\n\t\t\t\t// (the browser seems to not know yet that a new one exists now) and doesn't reach the handler\n\t\t\t\t// https://github.com/sveltejs/svelte/issues/11903\n\t\t\t\tif (value != null) continue;\n\n\t\t\t\telement.removeEventListener(event_name, current[event_handle_key], opts);\n\t\t\t\tcurrent[event_handle_key] = null;\n\t\t\t}\n\n\t\t\tif (value != null) {\n\t\t\t\tif (!delegated) {\n\t\t\t\t\t/**\n\t\t\t\t\t * @this {any}\n\t\t\t\t\t * @param {Event} evt\n\t\t\t\t\t */\n\t\t\t\t\tfunction handle(evt) {\n\t\t\t\t\t\tcurrent[key].call(this, evt);\n\t\t\t\t\t}\n\n\t\t\t\t\tcurrent[event_handle_key] = create_event(event_name, element, handle, opts);\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\telement[`__${event_name}`] = value;\n\t\t\t\t\tdelegate([event_name]);\n\t\t\t\t}\n\t\t\t} else if (delegated) {\n\t\t\t\t// @ts-ignore\n\t\t\t\telement[`__${event_name}`] = undefined;\n\t\t\t}\n\t\t} else if (key === 'style') {\n\t\t\t// avoid using the setter\n\t\t\tset_attribute(element, key, value);\n\t\t} else if (key === 'autofocus') {\n\t\t\tautofocus(/** @type {HTMLElement} */ (element), Boolean(value));\n\t\t} else if (!is_custom_element && (key === '__value' || (key === 'value' && value != null))) {\n\t\t\t// @ts-ignore We're not running this for custom elements because __value is actually\n\t\t\t// how Lit stores the current value on the element, and messing with that would break things.\n\t\t\telement.value = element.__value = value;\n\t\t} else if (key === 'selected' && is_option_element) {\n\t\t\tset_selected(/** @type {HTMLOptionElement} */ (element), value);\n\t\t} else {\n\t\t\tvar name = key;\n\t\t\tif (!preserve_attribute_case) {\n\t\t\t\tname = normalize_attribute(name);\n\t\t\t}\n\n\t\t\tvar is_default = name === 'defaultValue' || name === 'defaultChecked';\n\n\t\t\tif (value == null && !is_custom_element && !is_default) {\n\t\t\t\tattributes[key] = null;\n\n\t\t\t\tif (name === 'value' || name === 'checked') {\n\t\t\t\t\t// removing value/checked also removes defaultValue/defaultChecked — preserve\n\t\t\t\t\tlet input = /** @type {HTMLInputElement} */ (element);\n\t\t\t\t\tconst use_default = prev === undefined;\n\t\t\t\t\tif (name === 'value') {\n\t\t\t\t\t\tlet previous = input.defaultValue;\n\t\t\t\t\t\tinput.removeAttribute(name);\n\t\t\t\t\t\tinput.defaultValue = previous;\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\tinput.value = input.__value = use_default ? previous : null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tlet previous = input.defaultChecked;\n\t\t\t\t\t\tinput.removeAttribute(name);\n\t\t\t\t\t\tinput.defaultChecked = previous;\n\t\t\t\t\t\tinput.checked = use_default ? previous : false;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\telement.removeAttribute(key);\n\t\t\t\t}\n\t\t\t} else if (\n\t\t\t\tis_default ||\n\t\t\t\t(setters.includes(name) && (is_custom_element || typeof value !== 'string'))\n\t\t\t) {\n\t\t\t\t// @ts-ignore\n\t\t\t\telement[name] = value;\n\t\t\t} else if (typeof value !== 'function') {\n\t\t\t\tset_attribute(element, name, value, skip_warning);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (is_hydrating_custom_element) {\n\t\tset_hydrating(true);\n\t}\n\n\treturn current;\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} element\n * @param {(...expressions: any) => Record<string | symbol, any>} fn\n * @param {Array<() => any>} thunks\n * @param {string} [css_hash]\n * @param {boolean} [skip_warning]\n */\nexport function attribute_effect(\n\telement,\n\tfn,\n\tthunks = [],\n\tcss_hash,\n\tskip_warning = false,\n\td = derived\n) {\n\tconst deriveds = thunks.map(d);\n\n\t/** @type {Record<string | symbol, any> | undefined} */\n\tvar prev = undefined;\n\n\t/** @type {Record<symbol, Effect>} */\n\tvar effects = {};\n\n\tvar is_select = element.nodeName === 'SELECT';\n\tvar inited = false;\n\n\tblock(() => {\n\t\tvar next = fn(...deriveds.map(get));\n\n\t\tset_attributes(element, prev, next, css_hash, skip_warning);\n\n\t\tif (inited && is_select && 'value' in next) {\n\t\t\tselect_option(/** @type {HTMLSelectElement} */ (element), next.value, false);\n\t\t}\n\n\t\tfor (let symbol of Object.getOwnPropertySymbols(effects)) {\n\t\t\tif (!next[symbol]) destroy_effect(effects[symbol]);\n\t\t}\n\n\t\tfor (let symbol of Object.getOwnPropertySymbols(next)) {\n\t\t\tvar n = next[symbol];\n\n\t\t\tif (symbol.description === ATTACHMENT_KEY && (!prev || n !== prev[symbol])) {\n\t\t\t\tif (effects[symbol]) destroy_effect(effects[symbol]);\n\t\t\t\teffects[symbol] = branch(() => attach(element, () => n));\n\t\t\t}\n\t\t}\n\n\t\tprev = next;\n\t});\n\n\tif (is_select) {\n\t\tinit_select(\n\t\t\t/** @type {HTMLSelectElement} */ (element),\n\t\t\t() => /** @type {Record<string | symbol, any>} */ (prev).value\n\t\t);\n\t}\n\n\tinited = true;\n}\n\n/**\n *\n * @param {Element} element\n */\nfunction get_attributes(element) {\n\treturn /** @type {Record<string | symbol, unknown>} **/ (\n\t\t// @ts-expect-error\n\t\telement.__attributes ??= {\n\t\t\t[IS_CUSTOM_ELEMENT]: element.nodeName.includes('-'),\n\t\t\t[IS_HTML]: element.namespaceURI === NAMESPACE_HTML\n\t\t}\n\t);\n}\n\n/** @type {Map<string, string[]>} */\nvar setters_cache = new Map();\n\n/** @param {Element} element */\nfunction get_setters(element) {\n\tvar setters = setters_cache.get(element.nodeName);\n\tif (setters) return setters;\n\tsetters_cache.set(element.nodeName, (setters = []));\n\n\tvar descriptors;\n\tvar proto = element; // In the case of custom elements there might be setters on the instance\n\tvar element_proto = Element.prototype;\n\n\t// Stop at Element, from there on there's only unnecessary setters we're not interested in\n\t// Do not use contructor.name here as that's unreliable in some browser environments\n\twhile (element_proto !== proto) {\n\t\tdescriptors = get_descriptors(proto);\n\n\t\tfor (var key in descriptors) {\n\t\t\tif (descriptors[key].set) {\n\t\t\t\tsetters.push(key);\n\t\t\t}\n\t\t}\n\n\t\tproto = get_prototype_of(proto);\n\t}\n\n\treturn setters;\n}\n\n/**\n * @param {any} element\n * @param {string} attribute\n * @param {string} value\n */\nfunction check_src_in_dev_hydration(element, attribute, value) {\n\tif (!DEV) return;\n\tif (attribute === 'srcset' && srcset_url_equal(element, value)) return;\n\tif (src_url_equal(element.getAttribute(attribute) ?? '', value)) return;\n\n\tw.hydration_attribute_changed(\n\t\tattribute,\n\t\telement.outerHTML.replace(element.innerHTML, element.innerHTML && '...'),\n\t\tString(value)\n\t);\n}\n\n/**\n * @param {string} element_src\n * @param {string} url\n * @returns {boolean}\n */\nfunction src_url_equal(element_src, url) {\n\tif (element_src === url) return true;\n\treturn new URL(element_src, document.baseURI).href === new URL(url, document.baseURI).href;\n}\n\n/** @param {string} srcset */\nfunction split_srcset(srcset) {\n\treturn srcset.split(',').map((src) => src.trim().split(' ').filter(Boolean));\n}\n\n/**\n * @param {HTMLSourceElement | HTMLImageElement} element\n * @param {string} srcset\n * @returns {boolean}\n */\nfunction srcset_url_equal(element, srcset) {\n\tvar element_urls = split_srcset(element.srcset);\n\tvar urls = split_srcset(srcset);\n\n\treturn (\n\t\turls.length === element_urls.length &&\n\t\turls.every(\n\t\t\t([url, width], i) =>\n\t\t\t\twidth === element_urls[i][1] &&\n\t\t\t\t// We need to test both ways because Vite will create an a full URL with\n\t\t\t\t// `new URL(asset, import.meta.url).href` for the client when `base: './'`, and the\n\t\t\t\t// relative URLs inside srcset are not automatically resolved to absolute URLs by\n\t\t\t\t// browsers (in contrast to img.src). This means both SSR and DOM code could\n\t\t\t\t// contain relative or absolute URLs.\n\t\t\t\t(src_url_equal(element_urls[i][0], url) || src_url_equal(url, element_urls[i][0]))\n\t\t)\n\t);\n}\n", "import { effect, teardown } from '../../../reactivity/effects.js';\nimport { untrack } from '../../../runtime.js';\n\n/**\n * Resize observer singleton.\n * One listener per element only!\n * https://groups.google.com/a/chromium.org/g/blink-dev/c/z6ienONUb5A/m/F5-VcUZtBAAJ\n */\nclass ResizeObserverSingleton {\n\t/** */\n\t#listeners = new WeakMap();\n\n\t/** @type {ResizeObserver | undefined} */\n\t#observer;\n\n\t/** @type {ResizeObserverOptions} */\n\t#options;\n\n\t/** @static */\n\tstatic entries = new WeakMap();\n\n\t/** @param {ResizeObserverOptions} options */\n\tconstructor(options) {\n\t\tthis.#options = options;\n\t}\n\n\t/**\n\t * @param {Element} element\n\t * @param {(entry: ResizeObserverEntry) => any} listener\n\t */\n\tobserve(element, listener) {\n\t\tvar listeners = this.#listeners.get(element) || new Set();\n\t\tlisteners.add(listener);\n\n\t\tthis.#listeners.set(element, listeners);\n\t\tthis.#getObserver().observe(element, this.#options);\n\n\t\treturn () => {\n\t\t\tvar listeners = this.#listeners.get(element);\n\t\t\tlisteners.delete(listener);\n\n\t\t\tif (listeners.size === 0) {\n\t\t\t\tthis.#listeners.delete(element);\n\t\t\t\t/** @type {ResizeObserver} */ (this.#observer).unobserve(element);\n\t\t\t}\n\t\t};\n\t}\n\n\t#getObserver() {\n\t\treturn (\n\t\t\tthis.#observer ??\n\t\t\t(this.#observer = new ResizeObserver(\n\t\t\t\t/** @param {any} entries */ (entries) => {\n\t\t\t\t\tfor (var entry of entries) {\n\t\t\t\t\t\tResizeObserverSingleton.entries.set(entry.target, entry);\n\t\t\t\t\t\tfor (var listener of this.#listeners.get(entry.target) || []) {\n\t\t\t\t\t\t\tlistener(entry);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t))\n\t\t);\n\t}\n}\n\nvar resize_observer_content_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'content-box'\n});\n\nvar resize_observer_border_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'border-box'\n});\n\nvar resize_observer_device_pixel_content_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'device-pixel-content-box'\n});\n\n/**\n * @param {Element} element\n * @param {'contentRect' | 'contentBoxSize' | 'borderBoxSize' | 'devicePixelContentBoxSize'} type\n * @param {(entry: keyof ResizeObserverEntry) => void} set\n */\nexport function bind_resize_observer(element, type, set) {\n\tvar observer =\n\t\ttype === 'contentRect' || type === 'contentBoxSize'\n\t\t\t? resize_observer_content_box\n\t\t\t: type === 'borderBoxSize'\n\t\t\t\t? resize_observer_border_box\n\t\t\t\t: resize_observer_device_pixel_content_box;\n\n\tvar unsub = observer.observe(element, /** @param {any} entry */ (entry) => set(entry[type]));\n\tteardown(unsub);\n}\n\n/**\n * @param {HTMLElement} element\n * @param {'clientWidth' | 'clientHeight' | 'offsetWidth' | 'offsetHeight'} type\n * @param {(size: number) => void} set\n */\nexport function bind_element_size(element, type, set) {\n\tvar unsub = resize_observer_border_box.observe(element, () => set(element[type]));\n\n\teffect(() => {\n\t\t// The update could contain reads which should be ignored\n\t\tuntrack(() => set(element[type]));\n\t\treturn unsub;\n\t});\n}\n", "/** @import { StoreReferencesContainer } from '#client' */\n/** @import { Store } from '#shared' */\nimport { subscribe_to_store } from '../../../store/utils.js';\nimport { get as get_store } from '../../../store/shared/index.js';\nimport { define_property, noop } from '../../shared/utils.js';\nimport { get } from '../runtime.js';\nimport { teardown } from './effects.js';\nimport { mutable_source, set } from './sources.js';\n\n/**\n * Whether or not the prop currently being read is a store binding, as in\n * `<Child bind:x={$y} />`. If it is, we treat the prop as mutable even in\n * runes mode, and skip `binding_property_non_reactive` validation\n */\nlet is_store_binding = false;\n\nlet IS_UNMOUNTED = Symbol();\n\n/**\n * Gets the current value of a store. If the store isn't subscribed to yet, it will create a proxy\n * signal that will be updated when the store is. The store references container is needed to\n * track reassignments to stores and to track the correct component context.\n * @template V\n * @param {Store<V> | null | undefined} store\n * @param {string} store_name\n * @param {StoreReferencesContainer} stores\n * @returns {V}\n */\nexport function store_get(store, store_name, stores) {\n\tconst entry = (stores[store_name] ??= {\n\t\tstore: null,\n\t\tsource: mutable_source(undefined),\n\t\tunsubscribe: noop\n\t});\n\n\t// if the component that setup this is already unmounted we don't want to register a subscription\n\tif (entry.store !== store && !(IS_UNMOUNTED in stores)) {\n\t\tentry.unsubscribe();\n\t\tentry.store = store ?? null;\n\n\t\tif (store == null) {\n\t\t\tentry.source.v = undefined; // see synchronous callback comment below\n\t\t\tentry.unsubscribe = noop;\n\t\t} else {\n\t\t\tvar is_synchronous_callback = true;\n\n\t\t\tentry.unsubscribe = subscribe_to_store(store, (v) => {\n\t\t\t\tif (is_synchronous_callback) {\n\t\t\t\t\t// If the first updates to the store value (possibly multiple of them) are synchronously\n\t\t\t\t\t// inside a derived, we will hit the `state_unsafe_mutation` error if we `set` the value\n\t\t\t\t\tentry.source.v = v;\n\t\t\t\t} else {\n\t\t\t\t\tset(entry.source, v);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tis_synchronous_callback = false;\n\t\t}\n\t}\n\n\t// if the component that setup this stores is already unmounted the source will be out of sync\n\t// so we just use the `get` for the stores, less performant but it avoids to create a memory leak\n\t// and it will keep the value consistent\n\tif (store && IS_UNMOUNTED in stores) {\n\t\treturn get_store(store);\n\t}\n\n\treturn get(entry.source);\n}\n\n/**\n * Unsubscribe from a store if it's not the same as the one in the store references container.\n * We need this in addition to `store_get` because someone could unsubscribe from a store but\n * then never subscribe to the new one (if any), causing the subscription to stay open wrongfully.\n * @param {Store<any> | null | undefined} store\n * @param {string} store_name\n * @param {StoreReferencesContainer} stores\n */\nexport function store_unsub(store, store_name, stores) {\n\t/** @type {StoreReferencesContainer[''] | undefined} */\n\tlet entry = stores[store_name];\n\n\tif (entry && entry.store !== store) {\n\t\t// Don't reset store yet, so that store_get above can resubscribe to new store if necessary\n\t\tentry.unsubscribe();\n\t\tentry.unsubscribe = noop;\n\t}\n\n\treturn store;\n}\n\n/**\n * Sets the new value of a store and returns that value.\n * @template V\n * @param {Store<V>} store\n * @param {V} value\n * @returns {V}\n */\nexport function store_set(store, value) {\n\tstore.set(value);\n\treturn value;\n}\n\n/**\n * @param {StoreReferencesContainer} stores\n * @param {string} store_name\n */\nexport function invalidate_store(stores, store_name) {\n\tvar entry = stores[store_name];\n\tif (entry.store !== null) {\n\t\tstore_set(entry.store, entry.source.v);\n\t}\n}\n\n/**\n * Unsubscribes from all auto-subscribed stores on destroy\n * @returns {[StoreReferencesContainer, ()=>void]}\n */\nexport function setup_stores() {\n\t/** @type {StoreReferencesContainer} */\n\tconst stores = {};\n\n\tfunction cleanup() {\n\t\tteardown(() => {\n\t\t\tfor (var store_name in stores) {\n\t\t\t\tconst ref = stores[store_name];\n\t\t\t\tref.unsubscribe();\n\t\t\t}\n\t\t\tdefine_property(stores, IS_UNMOUNTED, {\n\t\t\t\tenumerable: false,\n\t\t\t\tvalue: true\n\t\t\t});\n\t\t});\n\t}\n\n\treturn [stores, cleanup];\n}\n\n/**\n * Updates a store with a new value.\n * @param {Store<V>} store  the store to update\n * @param {any} expression  the expression that mutates the store\n * @param {V} new_value  the new store value\n * @template V\n */\nexport function store_mutate(store, expression, new_value) {\n\tstore.set(new_value);\n\treturn expression;\n}\n\n/**\n * @param {Store<number>} store\n * @param {number} store_value\n * @param {1 | -1} [d]\n * @returns {number}\n */\nexport function update_store(store, store_value, d = 1) {\n\tstore.set(store_value + d);\n\treturn store_value;\n}\n\n/**\n * @param {Store<number>} store\n * @param {number} store_value\n * @param {1 | -1} [d]\n * @returns {number}\n */\nexport function update_pre_store(store, store_value, d = 1) {\n\tconst value = store_value + d;\n\tstore.set(value);\n\treturn value;\n}\n\n/**\n * Called inside prop getters to communicate that the prop is a store binding\n */\nexport function mark_store_binding() {\n\tis_store_binding = true;\n}\n\n/**\n * Returns a tuple that indicates whether `fn()` reads a prop that is a store binding.\n * Used to prevent `binding_property_non_reactive` validation false positives and\n * ensure that these props are treated as mutable even in runes mode\n * @template T\n * @param {() => T} fn\n * @returns {[T, boolean]}\n */\nexport function capture_store_binding(fn) {\n\tvar previous_is_store_binding = is_store_binding;\n\n\ttry {\n\t\tis_store_binding = false;\n\t\treturn [fn(), is_store_binding];\n\t} finally {\n\t\tis_store_binding = previous_is_store_binding;\n\t}\n}\n", "/** @import { ComponentConstructorOptions, ComponentType, SvelteComponent, Component } from 'svelte' */\nimport { DIRTY, LEGACY_PROPS, MAYBE_DIRTY } from '../internal/client/constants.js';\nimport { user_pre_effect } from '../internal/client/reactivity/effects.js';\nimport { mutable_source, set } from '../internal/client/reactivity/sources.js';\nimport { hydrate, mount, unmount } from '../internal/client/render.js';\nimport { active_effect, flushSync, get, set_signal_status } from '../internal/client/runtime.js';\nimport { lifecycle_outside_component } from '../internal/shared/errors.js';\nimport { define_property, is_array } from '../internal/shared/utils.js';\nimport * as w from '../internal/client/warnings.js';\nimport { DEV } from 'esm-env';\nimport { FILENAME } from '../constants.js';\nimport { component_context, dev_current_component_function } from '../internal/client/context.js';\n\n/**\n * Takes the same options as a Svelte 4 component and the component function and returns a Svelte 4 compatible component.\n *\n * @deprecated Use this only as a temporary solution to migrate your imperative component code to Svelte 5.\n *\n * @template {Record<string, any>} Props\n * @template {Record<string, any>} Exports\n * @template {Record<string, any>} Events\n * @template {Record<string, any>} Slots\n *\n * @param {ComponentConstructorOptions<Props> & {\n * \tcomponent: ComponentType<SvelteComponent<Props, Events, Slots>> | Component<Props>;\n * }} options\n * @returns {SvelteComponent<Props, Events, Slots> & Exports}\n */\nexport function createClassComponent(options) {\n\t// @ts-expect-error $$prop_def etc are not actually defined\n\treturn new Svelte4Component(options);\n}\n\n/**\n * Takes the component function and returns a Svelte 4 compatible component constructor.\n *\n * @deprecated Use this only as a temporary solution to migrate your imperative component code to Svelte 5.\n *\n * @template {Record<string, any>} Props\n * @template {Record<string, any>} Exports\n * @template {Record<string, any>} Events\n * @template {Record<string, any>} Slots\n *\n * @param {SvelteComponent<Props, Events, Slots> | Component<Props>} component\n * @returns {ComponentType<SvelteComponent<Props, Events, Slots> & Exports>}\n */\nexport function asClassComponent(component) {\n\t// @ts-expect-error $$prop_def etc are not actually defined\n\treturn class extends Svelte4Component {\n\t\t/** @param {any} options */\n\t\tconstructor(options) {\n\t\t\tsuper({\n\t\t\t\tcomponent,\n\t\t\t\t...options\n\t\t\t});\n\t\t}\n\t};\n}\n\n/**\n * Support using the component as both a class and function during the transition period\n * @typedef  {{new (o: ComponentConstructorOptions): SvelteComponent;(...args: Parameters<Component<Record<string, any>>>): ReturnType<Component<Record<string, any>, Record<string, any>>>;}} LegacyComponentType\n */\n\nclass Svelte4Component {\n\t/** @type {any} */\n\t#events;\n\n\t/** @type {Record<string, any>} */\n\t#instance;\n\n\t/**\n\t * @param {ComponentConstructorOptions & {\n\t *  component: any;\n\t * }} options\n\t */\n\tconstructor(options) {\n\t\tvar sources = new Map();\n\n\t\t/**\n\t\t * @param {string | symbol} key\n\t\t * @param {unknown} value\n\t\t */\n\t\tvar add_source = (key, value) => {\n\t\t\tvar s = mutable_source(value);\n\t\t\tsources.set(key, s);\n\t\t\treturn s;\n\t\t};\n\n\t\t// Replicate coarse-grained props through a proxy that has a version source for\n\t\t// each property, which is incremented on updates to the property itself. Do not\n\t\t// use our $state proxy because that one has fine-grained reactivity.\n\t\tconst props = new Proxy(\n\t\t\t{ ...(options.props || {}), $$events: {} },\n\t\t\t{\n\t\t\t\tget(target, prop) {\n\t\t\t\t\treturn get(sources.get(prop) ?? add_source(prop, Reflect.get(target, prop)));\n\t\t\t\t},\n\t\t\t\thas(target, prop) {\n\t\t\t\t\t// Necessary to not throw \"invalid binding\" validation errors on the component side\n\t\t\t\t\tif (prop === LEGACY_PROPS) return true;\n\n\t\t\t\t\tget(sources.get(prop) ?? add_source(prop, Reflect.get(target, prop)));\n\t\t\t\t\treturn Reflect.has(target, prop);\n\t\t\t\t},\n\t\t\t\tset(target, prop, value) {\n\t\t\t\t\tset(sources.get(prop) ?? add_source(prop, value), value);\n\t\t\t\t\treturn Reflect.set(target, prop, value);\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\n\t\tthis.#instance = (options.hydrate ? hydrate : mount)(options.component, {\n\t\t\ttarget: options.target,\n\t\t\tanchor: options.anchor,\n\t\t\tprops,\n\t\t\tcontext: options.context,\n\t\t\tintro: options.intro ?? false,\n\t\t\trecover: options.recover\n\t\t});\n\n\t\t// We don't flushSync for custom element wrappers or if the user doesn't want it\n\t\tif (!options?.props?.$$host || options.sync === false) {\n\t\t\tflushSync();\n\t\t}\n\n\t\tthis.#events = props.$$events;\n\n\t\tfor (const key of Object.keys(this.#instance)) {\n\t\t\tif (key === '$set' || key === '$destroy' || key === '$on') continue;\n\t\t\tdefine_property(this, key, {\n\t\t\t\tget() {\n\t\t\t\t\treturn this.#instance[key];\n\t\t\t\t},\n\t\t\t\t/** @param {any} value */\n\t\t\t\tset(value) {\n\t\t\t\t\tthis.#instance[key] = value;\n\t\t\t\t},\n\t\t\t\tenumerable: true\n\t\t\t});\n\t\t}\n\n\t\tthis.#instance.$set = /** @param {Record<string, any>} next */ (next) => {\n\t\t\tObject.assign(props, next);\n\t\t};\n\n\t\tthis.#instance.$destroy = () => {\n\t\t\tunmount(this.#instance);\n\t\t};\n\t}\n\n\t/** @param {Record<string, any>} props */\n\t$set(props) {\n\t\tthis.#instance.$set(props);\n\t}\n\n\t/**\n\t * @param {string} event\n\t * @param {(...args: any[]) => any} callback\n\t * @returns {any}\n\t */\n\t$on(event, callback) {\n\t\tthis.#events[event] = this.#events[event] || [];\n\n\t\t/** @param {any[]} args */\n\t\tconst cb = (...args) => callback.call(this, ...args);\n\t\tthis.#events[event].push(cb);\n\t\treturn () => {\n\t\t\tthis.#events[event] = this.#events[event].filter(/** @param {any} fn */ (fn) => fn !== cb);\n\t\t};\n\t}\n\n\t$destroy() {\n\t\tthis.#instance.$destroy();\n\t}\n}\n\n/**\n * Runs the given function once immediately on the server, and works like `$effect.pre` on the client.\n *\n * @deprecated Use this only as a temporary solution to migrate your component code to Svelte 5.\n * @param {() => void | (() => void)} fn\n * @returns {void}\n */\nexport function run(fn) {\n\tuser_pre_effect(() => {\n\t\tfn();\n\t\tvar effect = /** @type {import('#client').Effect} */ (active_effect);\n\t\t// If the effect is immediately made dirty again, mark it as maybe dirty to emulate legacy behaviour\n\t\tif ((effect.f & DIRTY) !== 0) {\n\t\t\tlet filename = \"a file (we can't know which one)\";\n\t\t\tif (DEV) {\n\t\t\t\t// @ts-ignore\n\t\t\t\tfilename = dev_current_component_function?.[FILENAME] ?? filename;\n\t\t\t}\n\t\t\tw.legacy_recursive_reactive_block(filename);\n\t\t\tset_signal_status(effect, MAYBE_DIRTY);\n\t\t}\n\t});\n}\n\n/**\n * Function to mimic the multiple listeners available in svelte 4\n * @deprecated\n * @param {EventListener[]} handlers\n * @returns {EventListener}\n */\nexport function handlers(...handlers) {\n\treturn function (event) {\n\t\tconst { stopImmediatePropagation } = event;\n\t\tlet stopped = false;\n\n\t\tevent.stopImmediatePropagation = () => {\n\t\t\tstopped = true;\n\t\t\tstopImmediatePropagation.call(event);\n\t\t};\n\n\t\tconst errors = [];\n\n\t\tfor (const handler of handlers) {\n\t\t\ttry {\n\t\t\t\t// @ts-expect-error `this` is not typed\n\t\t\t\thandler?.call(this, event);\n\t\t\t} catch (e) {\n\t\t\t\terrors.push(e);\n\t\t\t}\n\n\t\t\tif (stopped) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tfor (let error of errors) {\n\t\t\tqueueMicrotask(() => {\n\t\t\t\tthrow error;\n\t\t\t});\n\t\t}\n\t};\n}\n\n/**\n * Function to create a `bubble` function that mimic the behavior of `on:click` without handler available in svelte 4.\n * @deprecated Use this only as a temporary solution to migrate your automatically delegated events in Svelte 5.\n */\nexport function createBubbler() {\n\tconst active_component_context = component_context;\n\tif (active_component_context === null) {\n\t\tlifecycle_outside_component('createBubbler');\n\t}\n\n\treturn (/**@type {string}*/ type) => (/**@type {Event}*/ event) => {\n\t\tconst events = /** @type {Record<string, Function | Function[]>} */ (\n\t\t\tactive_component_context.s.$$events\n\t\t)?.[/** @type {any} */ (type)];\n\n\t\tif (events) {\n\t\t\tconst callbacks = is_array(events) ? events.slice() : [events];\n\t\t\tfor (const fn of callbacks) {\n\t\t\t\tfn.call(active_component_context.x, event);\n\t\t\t}\n\t\t\treturn !event.defaultPrevented;\n\t\t}\n\t\treturn true;\n\t};\n}\n\nexport {\n\tonce,\n\tpreventDefault,\n\tself,\n\tstopImmediatePropagation,\n\tstopPropagation,\n\ttrusted,\n\tpassive,\n\tnonpassive\n} from '../internal/client/dom/legacy/event-modifiers.js';\n", "import { createClassComponent } from '../../../../legacy/legacy-client.js';\nimport { effect_root, render_effect } from '../../reactivity/effects.js';\nimport { append } from '../template.js';\nimport { define_property, get_descriptor, object_keys } from '../../../shared/utils.js';\n\n/**\n * @typedef {Object} CustomElementPropDefinition\n * @property {string} [attribute]\n * @property {boolean} [reflect]\n * @property {'String'|'Boolean'|'Number'|'Array'|'Object'} [type]\n */\n\n/** @type {any} */\nlet SvelteElement;\n\nif (typeof HTMLElement === 'function') {\n\tSvelteElement = class extends HTMLElement {\n\t\t/** The Svelte component constructor */\n\t\t$$ctor;\n\t\t/** Slots */\n\t\t$$s;\n\t\t/** @type {any} The Svelte component instance */\n\t\t$$c;\n\t\t/** Whether or not the custom element is connected */\n\t\t$$cn = false;\n\t\t/** @type {Record<string, any>} Component props data */\n\t\t$$d = {};\n\t\t/** `true` if currently in the process of reflecting component props back to attributes */\n\t\t$$r = false;\n\t\t/** @type {Record<string, CustomElementPropDefinition>} Props definition (name, reflected, type etc) */\n\t\t$$p_d = {};\n\t\t/** @type {Record<string, EventListenerOrEventListenerObject[]>} Event listeners */\n\t\t$$l = {};\n\t\t/** @type {Map<EventListenerOrEventListenerObject, Function>} Event listener unsubscribe functions */\n\t\t$$l_u = new Map();\n\t\t/** @type {any} The managed render effect for reflecting attributes */\n\t\t$$me;\n\n\t\t/**\n\t\t * @param {*} $$componentCtor\n\t\t * @param {*} $$slots\n\t\t * @param {*} use_shadow_dom\n\t\t */\n\t\tconstructor($$componentCtor, $$slots, use_shadow_dom) {\n\t\t\tsuper();\n\t\t\tthis.$$ctor = $$componentCtor;\n\t\t\tthis.$$s = $$slots;\n\t\t\tif (use_shadow_dom) {\n\t\t\t\tthis.attachShadow({ mode: 'open' });\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * @param {string} type\n\t\t * @param {EventListenerOrEventListenerObject} listener\n\t\t * @param {boolean | AddEventListenerOptions} [options]\n\t\t */\n\t\taddEventListener(type, listener, options) {\n\t\t\t// We can't determine upfront if the event is a custom event or not, so we have to\n\t\t\t// listen to both. If someone uses a custom event with the same name as a regular\n\t\t\t// browser event, this fires twice - we can't avoid that.\n\t\t\tthis.$$l[type] = this.$$l[type] || [];\n\t\t\tthis.$$l[type].push(listener);\n\t\t\tif (this.$$c) {\n\t\t\t\tconst unsub = this.$$c.$on(type, listener);\n\t\t\t\tthis.$$l_u.set(listener, unsub);\n\t\t\t}\n\t\t\tsuper.addEventListener(type, listener, options);\n\t\t}\n\n\t\t/**\n\t\t * @param {string} type\n\t\t * @param {EventListenerOrEventListenerObject} listener\n\t\t * @param {boolean | AddEventListenerOptions} [options]\n\t\t */\n\t\tremoveEventListener(type, listener, options) {\n\t\t\tsuper.removeEventListener(type, listener, options);\n\t\t\tif (this.$$c) {\n\t\t\t\tconst unsub = this.$$l_u.get(listener);\n\t\t\t\tif (unsub) {\n\t\t\t\t\tunsub();\n\t\t\t\t\tthis.$$l_u.delete(listener);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tasync connectedCallback() {\n\t\t\tthis.$$cn = true;\n\t\t\tif (!this.$$c) {\n\t\t\t\t// We wait one tick to let possible child slot elements be created/mounted\n\t\t\t\tawait Promise.resolve();\n\t\t\t\tif (!this.$$cn || this.$$c) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t/** @param {string} name */\n\t\t\t\tfunction create_slot(name) {\n\t\t\t\t\t/**\n\t\t\t\t\t * @param {Element} anchor\n\t\t\t\t\t */\n\t\t\t\t\treturn (anchor) => {\n\t\t\t\t\t\tconst slot = document.createElement('slot');\n\t\t\t\t\t\tif (name !== 'default') slot.name = name;\n\n\t\t\t\t\t\tappend(anchor, slot);\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t/** @type {Record<string, any>} */\n\t\t\t\tconst $$slots = {};\n\t\t\t\tconst existing_slots = get_custom_elements_slots(this);\n\t\t\t\tfor (const name of this.$$s) {\n\t\t\t\t\tif (name in existing_slots) {\n\t\t\t\t\t\tif (name === 'default' && !this.$$d.children) {\n\t\t\t\t\t\t\tthis.$$d.children = create_slot(name);\n\t\t\t\t\t\t\t$$slots.default = true;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t$$slots[name] = create_slot(name);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (const attribute of this.attributes) {\n\t\t\t\t\t// this.$$data takes precedence over this.attributes\n\t\t\t\t\tconst name = this.$$g_p(attribute.name);\n\t\t\t\t\tif (!(name in this.$$d)) {\n\t\t\t\t\t\tthis.$$d[name] = get_custom_element_value(name, attribute.value, this.$$p_d, 'toProp');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// Port over props that were set programmatically before ce was initialized\n\t\t\t\tfor (const key in this.$$p_d) {\n\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\tif (!(key in this.$$d) && this[key] !== undefined) {\n\t\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\t\tthis.$$d[key] = this[key]; // don't transform, these were set through JavaScript\n\t\t\t\t\t\t// @ts-expect-error\n\t\t\t\t\t\tdelete this[key]; // remove the property that shadows the getter/setter\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$$c = createClassComponent({\n\t\t\t\t\tcomponent: this.$$ctor,\n\t\t\t\t\ttarget: this.shadowRoot || this,\n\t\t\t\t\tprops: {\n\t\t\t\t\t\t...this.$$d,\n\t\t\t\t\t\t$$slots,\n\t\t\t\t\t\t$$host: this\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// Reflect component props as attributes\n\t\t\t\tthis.$$me = effect_root(() => {\n\t\t\t\t\trender_effect(() => {\n\t\t\t\t\t\tthis.$$r = true;\n\t\t\t\t\t\tfor (const key of object_keys(this.$$c)) {\n\t\t\t\t\t\t\tif (!this.$$p_d[key]?.reflect) continue;\n\t\t\t\t\t\t\tthis.$$d[key] = this.$$c[key];\n\t\t\t\t\t\t\tconst attribute_value = get_custom_element_value(\n\t\t\t\t\t\t\t\tkey,\n\t\t\t\t\t\t\t\tthis.$$d[key],\n\t\t\t\t\t\t\t\tthis.$$p_d,\n\t\t\t\t\t\t\t\t'toAttribute'\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tif (attribute_value == null) {\n\t\t\t\t\t\t\t\tthis.removeAttribute(this.$$p_d[key].attribute || key);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.setAttribute(this.$$p_d[key].attribute || key, attribute_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.$$r = false;\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\tfor (const type in this.$$l) {\n\t\t\t\t\tfor (const listener of this.$$l[type]) {\n\t\t\t\t\t\tconst unsub = this.$$c.$on(type, listener);\n\t\t\t\t\t\tthis.$$l_u.set(listener, unsub);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$$l = {};\n\t\t\t}\n\t\t}\n\n\t\t// We don't need this when working within Svelte code, but for compatibility of people using this outside of Svelte\n\t\t// and setting attributes through setAttribute etc, this is helpful\n\n\t\t/**\n\t\t * @param {string} attr\n\t\t * @param {string} _oldValue\n\t\t * @param {string} newValue\n\t\t */\n\t\tattributeChangedCallback(attr, _oldValue, newValue) {\n\t\t\tif (this.$$r) return;\n\t\t\tattr = this.$$g_p(attr);\n\t\t\tthis.$$d[attr] = get_custom_element_value(attr, newValue, this.$$p_d, 'toProp');\n\t\t\tthis.$$c?.$set({ [attr]: this.$$d[attr] });\n\t\t}\n\n\t\tdisconnectedCallback() {\n\t\t\tthis.$$cn = false;\n\t\t\t// In a microtask, because this could be a move within the DOM\n\t\t\tPromise.resolve().then(() => {\n\t\t\t\tif (!this.$$cn && this.$$c) {\n\t\t\t\t\tthis.$$c.$destroy();\n\t\t\t\t\tthis.$$me();\n\t\t\t\t\tthis.$$c = undefined;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * @param {string} attribute_name\n\t\t */\n\t\t$$g_p(attribute_name) {\n\t\t\treturn (\n\t\t\t\tobject_keys(this.$$p_d).find(\n\t\t\t\t\t(key) =>\n\t\t\t\t\t\tthis.$$p_d[key].attribute === attribute_name ||\n\t\t\t\t\t\t(!this.$$p_d[key].attribute && key.toLowerCase() === attribute_name)\n\t\t\t\t) || attribute_name\n\t\t\t);\n\t\t}\n\t};\n}\n\n/**\n * @param {string} prop\n * @param {any} value\n * @param {Record<string, CustomElementPropDefinition>} props_definition\n * @param {'toAttribute' | 'toProp'} [transform]\n */\nfunction get_custom_element_value(prop, value, props_definition, transform) {\n\tconst type = props_definition[prop]?.type;\n\tvalue = type === 'Boolean' && typeof value !== 'boolean' ? value != null : value;\n\tif (!transform || !props_definition[prop]) {\n\t\treturn value;\n\t} else if (transform === 'toAttribute') {\n\t\tswitch (type) {\n\t\t\tcase 'Object':\n\t\t\tcase 'Array':\n\t\t\t\treturn value == null ? null : JSON.stringify(value);\n\t\t\tcase 'Boolean':\n\t\t\t\treturn value ? '' : null;\n\t\t\tcase 'Number':\n\t\t\t\treturn value == null ? null : value;\n\t\t\tdefault:\n\t\t\t\treturn value;\n\t\t}\n\t} else {\n\t\tswitch (type) {\n\t\t\tcase 'Object':\n\t\t\tcase 'Array':\n\t\t\t\treturn value && JSON.parse(value);\n\t\t\tcase 'Boolean':\n\t\t\t\treturn value; // conversion already handled above\n\t\t\tcase 'Number':\n\t\t\t\treturn value != null ? +value : value;\n\t\t\tdefault:\n\t\t\t\treturn value;\n\t\t}\n\t}\n}\n\n/**\n * @param {HTMLElement} element\n */\nfunction get_custom_elements_slots(element) {\n\t/** @type {Record<string, true>} */\n\tconst result = {};\n\telement.childNodes.forEach((node) => {\n\t\tresult[/** @type {Element} node */ (node).slot || 'default'] = true;\n\t});\n\treturn result;\n}\n\n/**\n * @internal\n *\n * Turn a Svelte component into a custom element.\n * @param {any} Component  A Svelte component function\n * @param {Record<string, CustomElementPropDefinition>} props_definition  The props to observe\n * @param {string[]} slots  The slots to create\n * @param {string[]} exports  Explicitly exported values, other than props\n * @param {boolean} use_shadow_dom  Whether to use shadow DOM\n * @param {(ce: new () => HTMLElement) => new () => HTMLElement} [extend]\n */\nexport function create_custom_element(\n\tComponent,\n\tprops_definition,\n\tslots,\n\texports,\n\tuse_shadow_dom,\n\textend\n) {\n\tlet Class = class extends SvelteElement {\n\t\tconstructor() {\n\t\t\tsuper(Component, slots, use_shadow_dom);\n\t\t\tthis.$$p_d = props_definition;\n\t\t}\n\t\tstatic get observedAttributes() {\n\t\t\treturn object_keys(props_definition).map((key) =>\n\t\t\t\t(props_definition[key].attribute || key).toLowerCase()\n\t\t\t);\n\t\t}\n\t};\n\tobject_keys(props_definition).forEach((prop) => {\n\t\tdefine_property(Class.prototype, prop, {\n\t\t\tget() {\n\t\t\t\treturn this.$$c && prop in this.$$c ? this.$$c[prop] : this.$$d[prop];\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tvalue = get_custom_element_value(prop, value, props_definition);\n\t\t\t\tthis.$$d[prop] = value;\n\t\t\t\tvar component = this.$$c;\n\n\t\t\t\tif (component) {\n\t\t\t\t\t// // If the instance has an accessor, use that instead\n\t\t\t\t\tvar setter = get_descriptor(component, prop)?.get;\n\n\t\t\t\t\tif (setter) {\n\t\t\t\t\t\tcomponent[prop] = value;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcomponent.$set({ [prop]: value });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t});\n\texports.forEach((property) => {\n\t\tdefine_property(Class.prototype, property, {\n\t\t\tget() {\n\t\t\t\treturn this.$$c?.[property];\n\t\t\t}\n\t\t});\n\t});\n\tif (extend) {\n\t\t// @ts-expect-error - assigning here is fine\n\t\tClass = extend(Class);\n\t}\n\tComponent.element = /** @type {any} */ Class;\n\treturn Class;\n}\n", "/** @import { ComponentContext, ComponentContextLegacy } from '#client' */\n/** @import { EventDispatcher } from './index.js' */\n/** @import { NotFunction } from './internal/types.js' */\nimport { untrack } from './internal/client/runtime.js';\nimport { is_array } from './internal/shared/utils.js';\nimport { user_effect } from './internal/client/index.js';\nimport * as e from './internal/client/errors.js';\nimport { lifecycle_outside_component } from './internal/shared/errors.js';\nimport { legacy_mode_flag } from './internal/flags/index.js';\nimport { component_context } from './internal/client/context.js';\nimport { DEV } from 'esm-env';\n\nif (DEV) {\n\t/**\n\t * @param {string} rune\n\t */\n\tfunction throw_rune_error(rune) {\n\t\tif (!(rune in globalThis)) {\n\t\t\t// TODO if people start adjusting the \"this can contain runes\" config through v-p-s more, adjust this message\n\t\t\t/** @type {any} */\n\t\t\tlet value; // let's hope noone modifies this global, but belts and braces\n\t\t\tObject.defineProperty(globalThis, rune, {\n\t\t\t\tconfigurable: true,\n\t\t\t\t// eslint-disable-next-line getter-return\n\t\t\t\tget: () => {\n\t\t\t\t\tif (value !== undefined) {\n\t\t\t\t\t\treturn value;\n\t\t\t\t\t}\n\n\t\t\t\t\te.rune_outside_svelte(rune);\n\t\t\t\t},\n\t\t\t\tset: (v) => {\n\t\t\t\t\tvalue = v;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n\n\tthrow_rune_error('$state');\n\tthrow_rune_error('$effect');\n\tthrow_rune_error('$derived');\n\tthrow_rune_error('$inspect');\n\tthrow_rune_error('$props');\n\tthrow_rune_error('$bindable');\n}\n\n/**\n * `onMount`, like [`$effect`](https://svelte.dev/docs/svelte/$effect), schedules a function to run as soon as the component has been mounted to the DOM.\n * Unlike `$effect`, the provided function only runs once.\n *\n * It must be called during the component's initialisation (but doesn't need to live _inside_ the component;\n * it can be called from an external module). If a function is returned _synchronously_ from `onMount`,\n * it will be called when the component is unmounted.\n *\n * `onMount` functions do not run during [server-side rendering](https://svelte.dev/docs/svelte/svelte-server#render).\n *\n * @template T\n * @param {() => NotFunction<T> | Promise<NotFunction<T>> | (() => any)} fn\n * @returns {void}\n */\nexport function onMount(fn) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component('onMount');\n\t}\n\n\tif (legacy_mode_flag && component_context.l !== null) {\n\t\tinit_update_callbacks(component_context).m.push(fn);\n\t} else {\n\t\tuser_effect(() => {\n\t\t\tconst cleanup = untrack(fn);\n\t\t\tif (typeof cleanup === 'function') return /** @type {() => void} */ (cleanup);\n\t\t});\n\t}\n}\n\n/**\n * Schedules a callback to run immediately before the component is unmounted.\n *\n * Out of `onMount`, `beforeUpdate`, `afterUpdate` and `onDestroy`, this is the\n * only one that runs inside a server-side component.\n *\n * @param {() => any} fn\n * @returns {void}\n */\nexport function onDestroy(fn) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component('onDestroy');\n\t}\n\n\tonMount(() => () => untrack(fn));\n}\n\n/**\n * @template [T=any]\n * @param {string} type\n * @param {T} [detail]\n * @param {any}params_0\n * @returns {CustomEvent<T>}\n */\nfunction create_custom_event(type, detail, { bubbles = false, cancelable = false } = {}) {\n\treturn new CustomEvent(type, { detail, bubbles, cancelable });\n}\n\n/**\n * Creates an event dispatcher that can be used to dispatch [component events](https://svelte.dev/docs/svelte/legacy-on#Component-events).\n * Event dispatchers are functions that can take two arguments: `name` and `detail`.\n *\n * Component events created with `createEventDispatcher` create a\n * [CustomEvent](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent).\n * These events do not [bubble](https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Building_blocks/Events#Event_bubbling_and_capture).\n * The `detail` argument corresponds to the [CustomEvent.detail](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/detail)\n * property and can contain any type of data.\n *\n * The event dispatcher can be typed to narrow the allowed event names and the type of the `detail` argument:\n * ```ts\n * const dispatch = createEventDispatcher<{\n *  loaded: null; // does not take a detail argument\n *  change: string; // takes a detail argument of type string, which is required\n *  optional: number | null; // takes an optional detail argument of type number\n * }>();\n * ```\n *\n * @deprecated Use callback props and/or the `$host()` rune instead — see [migration guide](https://svelte.dev/docs/svelte/v5-migration-guide#Event-changes-Component-events)\n * @template {Record<string, any>} [EventMap = any]\n * @returns {EventDispatcher<EventMap>}\n */\nexport function createEventDispatcher() {\n\tconst active_component_context = component_context;\n\tif (active_component_context === null) {\n\t\tlifecycle_outside_component('createEventDispatcher');\n\t}\n\n\treturn (type, detail, options) => {\n\t\tconst events = /** @type {Record<string, Function | Function[]>} */ (\n\t\t\tactive_component_context.s.$$events\n\t\t)?.[/** @type {any} */ (type)];\n\n\t\tif (events) {\n\t\t\tconst callbacks = is_array(events) ? events.slice() : [events];\n\t\t\t// TODO are there situations where events could be dispatched\n\t\t\t// in a server (non-DOM) environment?\n\t\t\tconst event = create_custom_event(/** @type {string} */ (type), detail, options);\n\t\t\tfor (const fn of callbacks) {\n\t\t\t\tfn.call(active_component_context.x, event);\n\t\t\t}\n\t\t\treturn !event.defaultPrevented;\n\t\t}\n\n\t\treturn true;\n\t};\n}\n\n// TODO mark beforeUpdate and afterUpdate as deprecated in Svelte 6\n\n/**\n * Schedules a callback to run immediately before the component is updated after any state change.\n *\n * The first time the callback runs will be before the initial `onMount`.\n *\n * In runes mode use `$effect.pre` instead.\n *\n * @deprecated Use [`$effect.pre`](https://svelte.dev/docs/svelte/$effect#$effect.pre) instead\n * @param {() => void} fn\n * @returns {void}\n */\nexport function beforeUpdate(fn) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component('beforeUpdate');\n\t}\n\n\tif (component_context.l === null) {\n\t\te.lifecycle_legacy_only('beforeUpdate');\n\t}\n\n\tinit_update_callbacks(component_context).b.push(fn);\n}\n\n/**\n * Schedules a callback to run immediately after the component has been updated.\n *\n * The first time the callback runs will be after the initial `onMount`.\n *\n * In runes mode use `$effect` instead.\n *\n * @deprecated Use [`$effect`](https://svelte.dev/docs/svelte/$effect) instead\n * @param {() => void} fn\n * @returns {void}\n */\nexport function afterUpdate(fn) {\n\tif (component_context === null) {\n\t\tlifecycle_outside_component('afterUpdate');\n\t}\n\n\tif (component_context.l === null) {\n\t\te.lifecycle_legacy_only('afterUpdate');\n\t}\n\n\tinit_update_callbacks(component_context).a.push(fn);\n}\n\n/**\n * Legacy-mode: Init callbacks object for onMount/beforeUpdate/afterUpdate\n * @param {ComponentContext} context\n */\nfunction init_update_callbacks(context) {\n\tvar l = /** @type {ComponentContextLegacy} */ (context).l;\n\treturn (l.u ??= { a: [], b: [], m: [] });\n}\n\nexport { flushSync } from './internal/client/runtime.js';\nexport { getContext, getAllContexts, hasContext, setContext } from './internal/client/context.js';\nexport { hydrate, mount, unmount } from './internal/client/render.js';\nexport { tick, untrack } from './internal/client/runtime.js';\nexport { createRawSnippet } from './internal/client/dom/blocks/snippet.js';\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAO,eAAQ;;;ACER,IAAI,WAAW,MAAM;AACrB,IAAI,WAAW,MAAM,UAAU;AAC/B,IAAI,aAAa,MAAM;AACvB,IAAI,cAAc,OAAO;AACzB,IAAI,kBAAkB,OAAO;AAC7B,IAAI,iBAAiB,OAAO;AAE5B,IAAI,mBAAmB,OAAO;AAC9B,IAAI,kBAAkB,MAAM;AAC5B,IAAI,mBAAmB,OAAO;AAC9B,IAAI,gBAAgB,OAAO;AA8B3B,SAAS,QAAQ,KAAK;AAC5B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,QAAI,CAAC,EAAE;AAAA,EACR;AACD;;;AC9CO,IAAM,UAAU,KAAK;AACrB,IAAM,SAAS,KAAK;AACpB,IAAM,gBAAgB,KAAK;AAC3B,IAAM,eAAe,KAAK;AAC1B,IAAM,gBAAgB,KAAK;AAC3B,IAAM,cAAc,KAAK;AACzB,IAAM,kBAAkB,KAAK;AAC7B,IAAM,UAAU,KAAK;AACrB,IAAM,eAAe,KAAK;AAC1B,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,cAAc,KAAK;AACzB,IAAM,QAAQ,KAAK;AACnB,IAAM,YAAY,KAAK;AACvB,IAAM,aAAa,KAAK;AAExB,IAAM,qBAAqB,KAAK;AAEhC,IAAM,sBAAsB,KAAK;AACjC,IAAM,iBAAiB,KAAK;AAC5B,IAAM,cAAc,KAAK;AACzB,IAAM,qBAAqB,KAAK;AAChC,IAAM,qBAAqB,KAAK;AAEhC,IAAM,eAAe,OAAO,QAAQ;AACpC,IAAM,eAAe,OAAO,cAAc;AAC1C,IAAM,sBAAsB,OAAO,EAAE;;;ACvBrC,SAAS,OAAO,OAAO;AAC7B,SAAO,UAAU,KAAK;AACvB;AAOO,SAAS,eAAe,GAAG,GAAG;AACpC,SAAO,KAAK,IACT,KAAK,IACL,MAAM,KAAM,MAAM,QAAQ,OAAO,MAAM,YAAa,OAAO,MAAM;AACrE;AAYO,SAAS,YAAY,OAAO;AAClC,SAAO,CAAC,eAAe,OAAO,KAAK,CAAC;AACrC;;;AC+DO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAA4H;AAEpJ,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAyBO,SAAS,mBAAmB,MAAM;AACxC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAyB,IAAI;AAAA,wCAA8F;AAEnJ,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC1D;AACD;AAMO,SAAS,4BAA4B;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,+CAA8K;AAEtM,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,gDAAgD;AAAA,EACjE;AACD;AAOO,SAAS,cAAc,MAAM;AACnC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAoB,IAAI;AAAA,mCAAiH;AAEjK,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACrD;AACD;AAMO,SAAS,+BAA+B;AAC9C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,kDAAmQ;AAE3R,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,mDAAmD;AAAA,EACpE;AACD;AAMO,SAAS,mBAAmB;AAClC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,sCAA4F;AAEpH,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,uCAAuC;AAAA,EACxD;AACD;AAsBO,SAAS,sBAAsB,MAAM;AAC3C,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAA4B,IAAI;AAAA,2CAAkF;AAE1I,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAuCO,SAAS,oBAAoB,MAAM;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,QAA8B,IAAI;AAAA,yCAAoH;AAE9K,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC3D;AACD;AAMO,SAAS,0BAA0B;AACzC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,6CAAmN;AAE3O,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AACD;AAMO,SAAS,wBAAwB;AACvC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,2CAA8G;AAEtI,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;AAMO,SAAS,wBAAwB;AACvC,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA;AAAA,2CAA8M;AAEtO,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC7D;AACD;;;ACjUO,IAAI,mBAAmB;AACvB,IAAI,oBAAoB;;;ACAxB,IAAM,sBAAsB,KAAK;AAEjC,IAAM,qBAAqB,KAAK;AAChC,IAAM,mBAAmB,KAAK;AAC9B,IAAM,sBAAsB,KAAK;AAGjC,IAAM,iBAAiB,KAAK;AAC5B,IAAM,mBAAmB,KAAK;AAC9B,IAAM,oBAAoB,KAAK;AAC/B,IAAM,wBAAwB,KAAK;AAGnC,IAAM,iBAAiB,KAAK;AAC5B,IAAM,oBAAoB,KAAK;AAG/B,IAAM,2BAA2B,KAAK;AACtC,IAAM,mBAAmB,KAAK;AAC9B,IAAM,sBAAsB,KAAK;AAEjC,IAAM,kBAAkB;AAGxB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB,CAAC;AAGzB,IAAM,kCAAkC,KAAK;AAE7C,IAAM,gBAAgB,OAAO;AAG7B,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,MAAM,OAAO,KAAK;;;AC1BxB,IAAI,sBAAsB;AAoI1B,SAAS,UAAU,OAAO;AAChC,MAAI,QAAQ,MAAM;AAClB,QAAMA,SAAQ,MAAM;AAEpB,MAAIA,QAAO;AACV,UAAM,QAAQA,OAAM,MAAM,IAAI;AAC9B,UAAM,YAAY,CAAC,IAAI;AAEvB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,YAAM,OAAO,MAAM,CAAC;AAEpB,UAAI,SAAS,SAAS;AACrB;AAAA,MACD;AACA,UAAI,KAAK,SAAS,oBAAoB,GAAG;AACxC,eAAO;AAAA,MACR;AACA,UAAI,KAAK,SAAS,qBAAqB,GAAG;AACzC;AAAA,MACD;AACA,gBAAU,KAAK,IAAI;AAAA,IACpB;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,aAAO;AAAA,IACR;AAEA,oBAAgB,OAAO,SAAS;AAAA,MAC/B,OAAO,UAAU,KAAK,IAAI;AAAA,IAC3B,CAAC;AAED,oBAAgB,OAAO,QAAQ;AAAA;AAAA,MAE9B,OAAO,GAAG,KAAK;AAAA,IAChB,CAAC;AAAA,EACF;AACA,SAAO;AACR;;;AC3IO,SAAS,4BAA4B,MAAM;AACjD,MAAI,cAAK;AACR,UAAM,QAAQ,IAAI,MAAM;AAAA,IAAkC,IAAI;AAAA,iDAA4G;AAE1K,UAAM,OAAO;AACb,UAAM;AAAA,EACP,OAAO;AACN,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACnE;AACD;;;ACjCO,IAAI,oBAAoB;AAGxB,SAAS,sBAAsB,SAAS;AAC9C,sBAAoB;AACrB;AAYO,IAAI,iCAAiC;AAGrC,SAAS,mCAAmC,IAAI;AACtD,mCAAiC;AAClC;AAUO,SAAS,WAAW,KAAK;AAC/B,QAAM,cAAc,wBAAwB,YAAY;AACxD,QAAM;AAAA;AAAA,IAA2B,YAAY,IAAI,GAAG;AAAA;AACpD,SAAO;AACR;AAcO,SAAS,WAAW,KAAK,SAAS;AACxC,QAAM,cAAc,wBAAwB,YAAY;AACxD,cAAY,IAAI,KAAK,OAAO;AAC5B,SAAO;AACR;AASO,SAAS,WAAW,KAAK;AAC/B,QAAM,cAAc,wBAAwB,YAAY;AACxD,SAAO,YAAY,IAAI,GAAG;AAC3B;AAUO,SAAS,iBAAiB;AAChC,QAAM,cAAc,wBAAwB,gBAAgB;AAC5D;AAAA;AAAA,IAAyB;AAAA;AAC1B;AAQO,SAAS,KAAK,OAAO,QAAQ,OAAO,IAAI;AAC9C,MAAI,MAAO,oBAAoB;AAAA,IAC9B,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACJ;AAEA,MAAI,oBAAoB,CAAC,OAAO;AAC/B,sBAAkB,IAAI;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI,CAAC;AAAA,MACL,IAAI,OAAO,KAAK;AAAA,IACjB;AAAA,EACD;AAEA,WAAS,MAAM;AACkB,IAAC,IAAK,IAAI;AAAA,EAC3C,CAAC;AAED,MAAI,cAAK;AAER,sBAAkB,WAAW;AAC7B,qCAAiC;AAAA,EAClC;AACD;AAOO,SAAS,IAAIC,YAAW;AA3I/B;AA4IC,QAAM,qBAAqB;AAC3B,MAAI,uBAAuB,MAAM;AAChC,QAAIA,eAAc,QAAW;AAC5B,yBAAmB,IAAIA;AAAA,IACxB;AACA,UAAM,oBAAoB,mBAAmB;AAC7C,QAAI,sBAAsB,MAAM;AAC/B,UAAI,kBAAkB;AACtB,UAAI,oBAAoB;AACxB,yBAAmB,IAAI;AACvB,UAAI;AACH,iBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AAClD,cAAI,mBAAmB,kBAAkB,CAAC;AAC1C,4BAAkB,iBAAiB,MAAM;AACzC,8BAAoB,iBAAiB,QAAQ;AAC7C,iBAAO,iBAAiB,EAAE;AAAA,QAC3B;AAAA,MACD,UAAE;AACD,0BAAkB,eAAe;AACjC,4BAAoB,iBAAiB;AAAA,MACtC;AAAA,IACD;AACA,wBAAoB,mBAAmB;AACvC,QAAI,cAAK;AACR,yCAAiC,wBAAmB,MAAnB,mBAAsB,aAAY;AAAA,IACpE;AACA,uBAAmB,IAAI;AAAA,EACxB;AAGA,SAAOA;AAAA,EAA+B,CAAC;AACxC;AAGO,SAAS,WAAW;AAC1B,SAAO,CAAC,oBAAqB,sBAAsB,QAAQ,kBAAkB,MAAM;AACpF;AAMA,SAAS,wBAAwB,MAAM;AACtC,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,IAAI;AAAA,EACjC;AAEA,SAAQ,kBAAkB,MAAlB,kBAAkB,IAAM,IAAI,IAAI,mBAAmB,iBAAiB,KAAK,MAAS;AAC3F;AAMA,SAAS,mBAAmBC,oBAAmB;AAC9C,MAAI,SAASA,mBAAkB;AAC/B,SAAO,WAAW,MAAM;AACvB,UAAM,cAAc,OAAO;AAC3B,QAAI,gBAAgB,MAAM;AACzB,aAAO;AAAA,IACR;AACA,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;;;ACtLO,SAAS,MAAM,OAAO;AAE5B,MAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,gBAAgB,OAAO;AACzE,WAAO;AAAA,EACR;AAEA,QAAM,YAAY,iBAAiB,KAAK;AAExC,MAAI,cAAc,oBAAoB,cAAc,iBAAiB;AACpE,WAAO;AAAA,EACR;AAGA,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,mBAAmB,SAAS,KAAK;AACrC,MAAI,UAAU,MAAO,CAAC;AAEtB,MAAIC,SAAQ,gBAAO,oBAAoB,UAAU,WAAW,IAAI;AAChE,MAAI,WAAW;AAMf,MAAI,cAAc,CAAC,OAAO;AACzB,QAAI,oBAAoB;AACxB,wBAAoB,QAAQ;AAG5B,QAAI,SAAS,GAAG;AAEhB,wBAAoB,iBAAiB;AACrC,WAAO;AAAA,EACR;AAEA,MAAI,kBAAkB;AAGrB,YAAQ,IAAI,UAAU;AAAA;AAAA,MAA6B,MAAO;AAAA,MAAQA;AAAA,IAAK,CAAC;AAAA,EACzE;AAEA,SAAO,IAAI;AAAA;AAAA,IAA0B;AAAA,IAAQ;AAAA,MAC5C,eAAe,GAAGC,OAAM,YAAY;AACnC,YACC,EAAE,WAAW,eACb,WAAW,iBAAiB,SAC5B,WAAW,eAAe,SAC1B,WAAW,aAAa,OACvB;AAKD,UAAE,wBAAwB;AAAA,QAC3B;AAEA,YAAI,IAAI,QAAQ,IAAIA,KAAI;AAExB,YAAI,MAAM,QAAW;AACpB,cAAI,YAAY,MAAM,MAAO,WAAW,OAAOD,MAAK,CAAC;AACrD,kBAAQ,IAAIC,OAAM,CAAC;AAAA,QACpB,OAAO;AACN;AAAA,YACC;AAAA,YACA,YAAY,MAAM,MAAM,WAAW,KAAK,CAAC;AAAA,UAC1C;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,eAAe,QAAQA,OAAM;AAC5B,YAAI,IAAI,QAAQ,IAAIA,KAAI;AAExB,YAAI,MAAM,QAAW;AACpB,cAAIA,SAAQ,QAAQ;AACnB,oBAAQ;AAAA,cACPA;AAAA,cACA,YAAY,MAAM,MAAO,eAAeD,MAAK,CAAC;AAAA,YAC/C;AACA,2BAAe,OAAO;AAAA,UACvB;AAAA,QACD,OAAO;AAGN,cAAI,oBAAoB,OAAOC,UAAS,UAAU;AACjD,gBAAI;AAAA;AAAA,cAAoC,QAAQ,IAAI,QAAQ;AAAA;AAC5D,gBAAI,IAAI,OAAOA,KAAI;AAEnB,gBAAI,OAAO,UAAU,CAAC,KAAK,IAAI,GAAG,GAAG;AACpC,kBAAI,IAAI,CAAC;AAAA,YACV;AAAA,UACD;AACA,cAAI,GAAG,aAAa;AACpB,yBAAe,OAAO;AAAA,QACvB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQA,OAAM,UAAU;AA1H9B;AA2HG,YAAIA,UAAS,cAAc;AAC1B,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,QAAQ,IAAIA,KAAI;AACxB,YAAI,SAASA,SAAQ;AAGrB,YAAI,MAAM,WAAc,CAAC,YAAU,oBAAe,QAAQA,KAAI,MAA3B,mBAA8B,YAAW;AAC3E,cAAI,YAAY,MAAM,MAAO,MAAM,SAAS,OAAOA,KAAI,IAAI,aAAa,GAAGD,MAAK,CAAC;AACjF,kBAAQ,IAAIC,OAAM,CAAC;AAAA,QACpB;AAEA,YAAI,MAAM,QAAW;AACpB,cAAI,IAAI,IAAI,CAAC;AACb,iBAAO,MAAM,gBAAgB,SAAY;AAAA,QAC1C;AAEA,eAAO,QAAQ,IAAI,QAAQA,OAAM,QAAQ;AAAA,MAC1C;AAAA,MAEA,yBAAyB,QAAQA,OAAM;AACtC,YAAI,aAAa,QAAQ,yBAAyB,QAAQA,KAAI;AAE9D,YAAI,cAAc,WAAW,YAAY;AACxC,cAAI,IAAI,QAAQ,IAAIA,KAAI;AACxB,cAAI,EAAG,YAAW,QAAQ,IAAI,CAAC;AAAA,QAChC,WAAW,eAAe,QAAW;AACpC,cAAIC,UAAS,QAAQ,IAAID,KAAI;AAC7B,cAAIE,SAAQD,WAAA,gBAAAA,QAAQ;AAEpB,cAAIA,YAAW,UAAaC,WAAU,eAAe;AACpD,mBAAO;AAAA,cACN,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,OAAAA;AAAA,cACA,UAAU;AAAA,YACX;AAAA,UACD;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQF,OAAM;AAvKpB;AAwKG,YAAIA,UAAS,cAAc;AAC1B,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,QAAQ,IAAIA,KAAI;AACxB,YAAI,MAAO,MAAM,UAAa,EAAE,MAAM,iBAAkB,QAAQ,IAAI,QAAQA,KAAI;AAEhF,YACC,MAAM,UACL,kBAAkB,SAAS,CAAC,SAAO,oBAAe,QAAQA,KAAI,MAA3B,mBAA8B,YACjE;AACD,cAAI,MAAM,QAAW;AACpB,gBAAI,YAAY,MAAM,MAAO,MAAM,MAAM,OAAOA,KAAI,CAAC,IAAI,eAAeD,MAAK,CAAC;AAC9E,oBAAQ,IAAIC,OAAM,CAAC;AAAA,UACpB;AAEA,cAAIE,SAAQ,IAAI,CAAC;AACjB,cAAIA,WAAU,eAAe;AAC5B,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,IAAI,QAAQF,OAAME,QAAO,UAAU;AAjMrC;AAkMG,YAAI,IAAI,QAAQ,IAAIF,KAAI;AACxB,YAAI,MAAMA,SAAQ;AAGlB,YAAI,oBAAoBA,UAAS,UAAU;AAC1C,mBAAS,IAAIE,QAAO;AAAA,UAAmC,EAAG,GAAG,KAAK,GAAG;AACpE,gBAAI,UAAU,QAAQ,IAAI,IAAI,EAAE;AAChC,gBAAI,YAAY,QAAW;AAC1B,kBAAI,SAAS,aAAa;AAAA,YAC3B,WAAW,KAAK,QAAQ;AAIvB,wBAAU,YAAY,MAAM,MAAO,eAAeH,MAAK,CAAC;AACxD,sBAAQ,IAAI,IAAI,IAAI,OAAO;AAAA,YAC5B;AAAA,UACD;AAAA,QACD;AAMA,YAAI,MAAM,QAAW;AACpB,cAAI,CAAC,SAAO,oBAAe,QAAQC,KAAI,MAA3B,mBAA8B,WAAU;AACnD,gBAAI,YAAY,MAAM,MAAO,QAAWD,MAAK,CAAC;AAC9C;AAAA,cACC;AAAA,cACA,YAAY,MAAM,MAAMG,MAAK,CAAC;AAAA,YAC/B;AACA,oBAAQ,IAAIF,OAAM,CAAC;AAAA,UACpB;AAAA,QACD,OAAO;AACN,gBAAM,EAAE,MAAM;AACd;AAAA,YACC;AAAA,YACA,YAAY,MAAM,MAAME,MAAK,CAAC;AAAA,UAC/B;AAAA,QACD;AAEA,YAAI,aAAa,QAAQ,yBAAyB,QAAQF,KAAI;AAG9D,YAAI,yCAAY,KAAK;AACpB,qBAAW,IAAI,KAAK,UAAUE,MAAK;AAAA,QACpC;AAEA,YAAI,CAAC,KAAK;AAKT,cAAI,oBAAoB,OAAOF,UAAS,UAAU;AACjD,gBAAI;AAAA;AAAA,cAAoC,QAAQ,IAAI,QAAQ;AAAA;AAC5D,gBAAI,IAAI,OAAOA,KAAI;AAEnB,gBAAI,OAAO,UAAU,CAAC,KAAK,KAAK,GAAG,GAAG;AACrC,kBAAI,IAAI,IAAI,CAAC;AAAA,YACd;AAAA,UACD;AAEA,yBAAe,OAAO;AAAA,QACvB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,QAAQ,QAAQ;AACf,YAAI,OAAO;AAEX,YAAI,WAAW,QAAQ,QAAQ,MAAM,EAAE,OAAO,CAACG,SAAQ;AACtD,cAAIF,UAAS,QAAQ,IAAIE,IAAG;AAC5B,iBAAOF,YAAW,UAAaA,QAAO,MAAM;AAAA,QAC7C,CAAC;AAED,iBAAS,CAAC,KAAKA,OAAM,KAAK,SAAS;AAClC,cAAIA,QAAO,MAAM,iBAAiB,EAAE,OAAO,SAAS;AACnD,qBAAS,KAAK,GAAG;AAAA,UAClB;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,iBAAiB;AAChB,QAAE,sBAAsB;AAAA,MACzB;AAAA,IACD;AAAA,EAAC;AACF;AAMA,SAAS,eAAe,QAAQ,IAAI,GAAG;AACtC,MAAI,QAAQ,OAAO,IAAI,CAAC;AACzB;AAKO,SAAS,kBAAkB,OAAO;AACxC,MAAI;AACH,QAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,gBAAgB,OAAO;AACzE,aAAO,MAAM,YAAY;AAAA,IAC1B;AAAA,EACD,QAAQ;AAAA,EAQR;AAEA,SAAO;AACR;;;ACxNO,SAAS,wBAAwBG,UAAS;AAChD,MAAI,UAAUA,SAAQ;AAEtB,MAAI,YAAY,MAAM;AACrB,IAAAA,SAAQ,UAAU;AAElB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AAC3C;AAAA;AAAA,QAAsC,QAAQ,CAAC;AAAA,MAAE;AAAA,IAClD;AAAA,EACD;AACD;AAOA,IAAI,QAAQ,CAAC;AAMb,SAAS,0BAA0BA,UAAS;AAC3C,MAAI,SAASA,SAAQ;AACrB,SAAO,WAAW,MAAM;AACvB,SAAK,OAAO,IAAI,aAAa,GAAG;AAC/B;AAAA;AAAA,QAA8B;AAAA;AAAA,IAC/B;AACA,aAAS,OAAO;AAAA,EACjB;AACA,SAAO;AACR;AAOO,SAAS,gBAAgBA,UAAS;AACxC,MAAI;AACJ,MAAI,qBAAqB;AAEzB,oBAAkB,0BAA0BA,QAAO,CAAC;AAEpD,MAAI,cAAK;AACR,QAAI,uBAAuB;AAC3B,wBAAoB,oBAAI,IAAI,CAAC;AAC7B,QAAI;AACH,UAAI,MAAM,SAASA,QAAO,GAAG;AAC5B,QAAE,wBAAwB;AAAA,MAC3B;AAEA,YAAM,KAAKA,QAAO;AAElB,8BAAwBA,QAAO;AAC/B,cAAQ,gBAAgBA,QAAO;AAAA,IAChC,UAAE;AACD,wBAAkB,kBAAkB;AACpC,0BAAoB,oBAAoB;AACxC,YAAM,IAAI;AAAA,IACX;AAAA,EACD,OAAO;AACN,QAAI;AACH,8BAAwBA,QAAO;AAC/B,cAAQ,gBAAgBA,QAAO;AAAA,IAChC,UAAE;AACD,wBAAkB,kBAAkB;AAAA,IACrC;AAAA,EACD;AAEA,SAAO;AACR;AAMO,SAAS,eAAeA,UAAS;AACvC,MAAI,QAAQ,gBAAgBA,QAAO;AAEnC,MAAI,CAACA,SAAQ,OAAO,KAAK,GAAG;AAC3B,IAAAA,SAAQ,IAAI;AACZ,IAAAA,SAAQ,KAAK,wBAAwB;AAAA,EACtC;AAIA,MAAI,qBAAsB;AAE1B,MAAI,UACF,kBAAkBA,SAAQ,IAAI,aAAa,MAAMA,SAAQ,SAAS,OAAO,cAAc;AAEzF,oBAAkBA,UAAS,MAAM;AAClC;;;ACvJO,IAAI,kBAAkB,oBAAI,IAAI;AAC9B,IAAM,aAAa,oBAAI,IAAI;AAK3B,SAAS,oBAAoB,GAAG;AACtC,oBAAkB;AACnB;AASO,SAAS,OAAO,GAAGC,QAAO;AAEhC,MAAI,SAAS;AAAA,IACZ,GAAG;AAAA;AAAA,IACH;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA,IAAI;AAAA,IACJ,IAAI;AAAA,EACL;AAEA,MAAI,gBAAO,mBAAmB;AAC7B,WAAO,UAAUA,UAAS,UAAU,WAAW;AAC/C,WAAO,QAAQ;AAAA,EAChB;AAEA,SAAO;AACR;AAQO,SAAS,MAAM,GAAGA,QAAO;AAC/B,QAAM,IAAI,OAAO,GAAGA,MAAK;AAEzB,sBAAoB,CAAC;AAErB,SAAO;AACR;AASO,SAAS,eAAe,eAAe,YAAY,OAAO;AA/FjE;AAgGC,QAAM,IAAI,OAAO,aAAa;AAC9B,MAAI,CAAC,WAAW;AACf,MAAE,SAAS;AAAA,EACZ;AAIA,MAAI,oBAAoB,sBAAsB,QAAQ,kBAAkB,MAAM,MAAM;AACnF,MAAC,uBAAkB,GAAE,MAApB,GAAoB,IAAM,CAAC,IAAG,KAAK,CAAC;AAAA,EACtC;AAEA,SAAO;AACR;AAsBO,SAAS,IAAIC,SAAQ,OAAO,eAAe,OAAO;AAlIzD;AAmIC,MACC,oBAAoB,QACpB,CAAC,cACD,SAAS,MACR,gBAAgB,KAAK,UAAU,mBAAmB,KACnD,GAAC,6CAAkB,SAASA,WAC3B;AACD,IAAE,sBAAsB;AAAA,EACzB;AAEA,MAAI,YAAY,eAAe,MAAM,KAAK,IAAI;AAE9C,SAAO,aAAaA,SAAQ,SAAS;AACtC;AAQO,SAAS,aAAaA,SAAQ,OAAO;AAC3C,MAAI,CAACA,QAAO,OAAO,KAAK,GAAG;AAC1B,QAAI,YAAYA,QAAO;AAEvB,QAAI,sBAAsB;AACzB,iBAAW,IAAIA,SAAQ,KAAK;AAAA,IAC7B,OAAO;AACN,iBAAW,IAAIA,SAAQ,SAAS;AAAA,IACjC;AAEA,IAAAA,QAAO,IAAI;AAEX,QAAI,gBAAO,mBAAmB;AAC7B,MAAAA,QAAO,UAAU,UAAU,WAAW;AACtC,UAAI,iBAAiB,MAAM;AAC1B,QAAAA,QAAO,sBAAsB;AAC7B,QAAAA,QAAO,YAAPA,QAAO,UAAY;AAAA,MACpB;AAAA,IACD;AAEA,SAAKA,QAAO,IAAI,aAAa,GAAG;AAE/B,WAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B;AAAA;AAAA,UAAwCA;AAAA,QAAO;AAAA,MAChD;AACA,wBAAkBA,UAASA,QAAO,IAAI,aAAa,IAAI,QAAQ,WAAW;AAAA,IAC3E;AAEA,IAAAA,QAAO,KAAK,wBAAwB;AAEpC,mBAAeA,SAAQ,KAAK;AAM5B,QACC,SAAS,KACT,kBAAkB,SACjB,cAAc,IAAI,WAAW,MAC7B,cAAc,KAAK,gBAAgB,kBAAkB,GACrD;AACD,UAAI,qBAAqB,MAAM;AAC9B,6BAAqB,CAACA,OAAM,CAAC;AAAA,MAC9B,OAAO;AACN,yBAAiB,KAAKA,OAAM;AAAA,MAC7B;AAAA,IACD;AAEA,QAAI,gBAAO,gBAAgB,OAAO,GAAG;AACpC,YAAM,WAAW,MAAM,KAAK,eAAe;AAE3C,iBAAWC,WAAU,UAAU;AAG9B,aAAKA,QAAO,IAAI,WAAW,GAAG;AAC7B,4BAAkBA,SAAQ,WAAW;AAAA,QACtC;AACA,YAAI,gBAAgBA,OAAM,GAAG;AAC5B,wBAAcA,OAAM;AAAA,QACrB;AAAA,MACD;AAEA,sBAAgB,MAAM;AAAA,IACvB;AAAA,EACD;AAEA,SAAO;AACR;AAoCA,SAAS,eAAe,QAAQ,QAAQ;AACvC,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc,KAAM;AAExB,MAAI,QAAQ,SAAS;AACrB,MAAI,SAAS,UAAU;AAEvB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,QAAI,WAAW,UAAU,CAAC;AAC1B,QAAI,QAAQ,SAAS;AAGrB,SAAK,QAAQ,WAAW,EAAG;AAG3B,QAAI,CAAC,SAAS,aAAa,cAAe;AAG1C,QAAI,iBAAQ,QAAQ,oBAAoB,GAAG;AAC1C,sBAAgB,IAAI,QAAQ;AAC5B;AAAA,IACD;AAEA,sBAAkB,UAAU,MAAM;AAGlC,SAAK,SAAS,QAAQ,cAAc,GAAG;AACtC,WAAK,QAAQ,aAAa,GAAG;AAC5B;AAAA;AAAA,UAAuC;AAAA,UAAW;AAAA,QAAW;AAAA,MAC9D,OAAO;AACN;AAAA;AAAA,UAAuC;AAAA,QAAS;AAAA,MACjD;AAAA,IACD;AAAA,EACD;AACD;;;AC9RA,IAAI,OAAO;AACX,IAAI,SAAS;AAmFN,SAAS,mBAAmB,UAAU;AAC5C,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,IAAoC,WAAW,mHAAmH,QAAQ,KAAK,wFAAwF;AAAA,0CAA6C,MAAM,MAAM;AAAA,EAC9U,OAAO;AACN,YAAQ,KAAK,yCAAyC;AAAA,EACvD;AACD;AAKO,SAAS,6BAA6B;AAC5C,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA;AAAA,kDAA4L,MAAM,MAAM;AAAA,EACtN,OAAO;AACN,YAAQ,KAAK,iDAAiD;AAAA,EAC/D;AACD;AAiBO,SAAS,2BAA2B;AAC1C,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA;AAAA,gDAA2I,MAAM,MAAM;AAAA,EACrK,OAAO;AACN,YAAQ,KAAK,+CAA+C;AAAA,EAC7D;AACD;AA+CO,SAAS,8BAA8B,UAAU;AACvD,MAAI,cAAK;AACR,YAAQ,KAAK;AAAA,8HAAyK,QAAQ;AAAA,qDAA0F,MAAM,MAAM;AAAA,EACrS,OAAO;AACN,YAAQ,KAAK,oDAAoD;AAAA,EAClE;AACD;;;ACtKO,IAAI,YAAY;AAGhB,SAAS,cAAc,OAAO;AACpC,cAAY;AACb;AASO,IAAI;AAGJ,SAAS,iBAAiB,MAAM;AACtC,MAAI,SAAS,MAAM;AAClB,IAAE,mBAAmB;AACrB,UAAM;AAAA,EACP;AAEA,SAAQ,eAAe;AACxB;AAEO,SAAS,eAAe;AAC9B,SAAO;AAAA;AAAA,IAA8C,iBAAiB,YAAY;AAAA,EAAE;AACrF;;;ACxCO,SAAS,gCAAgC;AAC/C,QAAMC,mBAAkB,MAAM;AAI9B,QAAM,UAAU,MAAM;AACtB,MAAI,SAAS;AACZ,YAAQ;AAAA,EACT;AAEA,QAAM,EAAE,SAAS,aAAa,SAAS,IAAIA;AAE3C,EAAAA,iBAAgB,UAAU,SAAU,MAAM,YAAY;AACrD,UAAMC,SAAQ,QAAQ,KAAK,MAAM,MAAM,UAAU;AAEjD,QAAIA,WAAU,IAAI;AACjB,eAAS,IAAI,cAAc,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACtD,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,oBAAoB;AACpD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAOA;AAAA,EACR;AAEA,EAAAD,iBAAgB,cAAc,SAAU,MAAM,YAAY;AAGzD,UAAMC,SAAQ,YAAY,KAAK,MAAM,MAAM,cAAc,KAAK,SAAS,CAAC;AAExE,QAAIA,WAAU,IAAI;AACjB,eAAS,IAAI,GAAG,MAAM,cAAc,KAAK,SAAS,IAAI,KAAK,GAAG;AAC7D,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,wBAAwB;AACxD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAOA;AAAA,EACR;AAEA,EAAAD,iBAAgB,WAAW,SAAU,MAAM,YAAY;AACtD,UAAM,MAAM,SAAS,KAAK,MAAM,MAAM,UAAU;AAEhD,QAAI,CAAC,KAAK;AACT,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACxC,YAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM,MAAM;AACxC,UAAE,8BAA8B,qBAAqB;AACrD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAGA,QAAM,mBAAmB,MAAM;AAC9B,IAAAA,iBAAgB,UAAU;AAC1B,IAAAA,iBAAgB,cAAc;AAC9B,IAAAA,iBAAgB,WAAW;AAAA,EAC5B;AACD;;;AC5DO,IAAI;AAGJ,IAAI;AAGJ,IAAI;AAGX,IAAI;AAEJ,IAAI;AAMG,SAAS,kBAAkB;AACjC,MAAI,YAAY,QAAW;AAC1B;AAAA,EACD;AAEA,YAAU;AACV,cAAY;AACZ,eAAa,UAAU,KAAK,UAAU,SAAS;AAE/C,MAAI,oBAAoB,QAAQ;AAChC,MAAI,iBAAiB,KAAK;AAC1B,MAAI,iBAAiB,KAAK;AAG1B,uBAAqB,eAAe,gBAAgB,YAAY,EAAE;AAElE,wBAAsB,eAAe,gBAAgB,aAAa,EAAE;AAEpE,MAAI,cAAc,iBAAiB,GAAG;AAGrC,sBAAkB,UAAU;AAE5B,sBAAkB,cAAc;AAEhC,sBAAkB,eAAe;AAEjC,sBAAkB,UAAU;AAE5B,sBAAkB,MAAM;AAAA,EACzB;AAEA,MAAI,cAAc,cAAc,GAAG;AAElC,mBAAe,MAAM;AAAA,EACtB;AAEA,MAAI,cAAK;AAER,sBAAkB,gBAAgB;AAElC,kCAA8B;AAAA,EAC/B;AACD;AAMO,SAAS,YAAY,QAAQ,IAAI;AACvC,SAAO,SAAS,eAAe,KAAK;AACrC;AAQO,SAAS,gBAAgB,MAAM;AACrC,SAAO,mBAAmB,KAAK,IAAI;AACpC;AAQO,SAAS,iBAAiB,MAAM;AACtC,SAAO,oBAAoB,KAAK,IAAI;AACrC;AA2GO,SAAS,mBAAmB,MAAM;AACxC,OAAK,cAAc;AACpB;;;AC9JO,SAAS,gBAAgB,MAAM;AACrC,MAAI,kBAAkB,QAAQ,oBAAoB,MAAM;AACvD,IAAE,cAAc,IAAI;AAAA,EACrB;AAEA,MAAI,oBAAoB,SAAS,gBAAgB,IAAI,aAAa,KAAK,kBAAkB,MAAM;AAC9F,IAAE,0BAA0B;AAAA,EAC7B;AAEA,MAAI,sBAAsB;AACzB,IAAE,mBAAmB,IAAI;AAAA,EAC1B;AACD;AAMA,SAAS,YAAYE,SAAQ,eAAe;AAC3C,MAAI,cAAc,cAAc;AAChC,MAAI,gBAAgB,MAAM;AACzB,kBAAc,OAAO,cAAc,QAAQA;AAAA,EAC5C,OAAO;AACN,gBAAY,OAAOA;AACnB,IAAAA,QAAO,OAAO;AACd,kBAAc,OAAOA;AAAA,EACtB;AACD;AASA,SAAS,cAAc,MAAM,IAAI,MAAMC,QAAO,MAAM;AACnD,MAAI,SAAS;AAEb,MAAI,cAAK;AAER,WAAO,WAAW,SAAS,OAAO,IAAI,oBAAoB,GAAG;AAC5D,eAAS,OAAO;AAAA,IACjB;AAAA,EACD;AAGA,MAAID,UAAS;AAAA,IACZ,KAAK;AAAA,IACL,MAAM;AAAA,IACN,aAAa;AAAA,IACb,WAAW;AAAA,IACX,GAAG,OAAO;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,IACN;AAAA,IACA,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,IAAI;AAAA,EACL;AAEA,MAAI,cAAK;AACR,IAAAA,QAAO,qBAAqB;AAAA,EAC7B;AAEA,MAAI,MAAM;AACT,QAAI;AACH,oBAAcA,OAAM;AACpB,MAAAA,QAAO,KAAK;AAAA,IACb,SAAS,GAAG;AACX,qBAAeA,OAAM;AACrB,YAAM;AAAA,IACP;AAAA,EACD,WAAW,OAAO,MAAM;AACvB,oBAAgBA,OAAM;AAAA,EACvB;AAIA,MAAI,QACH,QACAA,QAAO,SAAS,QAChBA,QAAO,UAAU,QACjBA,QAAO,gBAAgB,QACvBA,QAAO,aAAa,SACnBA,QAAO,KAAK,qBAAqB,sBAAsB;AAEzD,MAAI,CAAC,SAASC,OAAM;AACnB,QAAI,WAAW,MAAM;AACpB,kBAAYD,SAAQ,MAAM;AAAA,IAC3B;AAGA,QAAI,oBAAoB,SAAS,gBAAgB,IAAI,aAAa,GAAG;AACpE,UAAIE;AAAA;AAAA,QAAkC;AAAA;AACtC,OAACA,SAAQ,YAARA,SAAQ,UAAY,CAAC,IAAG,KAAKF,OAAM;AAAA,IACrC;AAAA,EACD;AAEA,SAAOA;AACR;AAaO,SAAS,SAAS,IAAI;AAC5B,QAAMG,UAAS,cAAc,eAAe,MAAM,KAAK;AACvD,oBAAkBA,SAAQ,KAAK;AAC/B,EAAAA,QAAO,WAAW;AAClB,SAAOA;AACR;AAMO,SAAS,YAAY,IAAI;AAC/B,kBAAgB,SAAS;AAIzB,MAAI,QACH,kBAAkB,SACjB,cAAc,IAAI,mBAAmB,KACtC,sBAAsB,QACtB,CAAC,kBAAkB;AAEpB,MAAI,cAAK;AACR,oBAAgB,IAAI,QAAQ;AAAA,MAC3B,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AAEA,MAAI,OAAO;AACV,QAAI;AAAA;AAAA,MAA2C;AAAA;AAC/C,KAAC,QAAQ,MAAR,QAAQ,IAAM,CAAC,IAAG,KAAK;AAAA,MACvB;AAAA,MACA,QAAQ;AAAA,MACR,UAAU;AAAA,IACX,CAAC;AAAA,EACF,OAAO;AACN,QAAI,SAAS,OAAO,EAAE;AACtB,WAAO;AAAA,EACR;AACD;AA2BO,SAAS,YAAY,IAAI;AAC/B,QAAMC,UAAS,cAAc,aAAa,IAAI,IAAI;AAElD,SAAO,MAAM;AACZ,mBAAeA,OAAM;AAAA,EACtB;AACD;AAOO,SAAS,eAAe,IAAI;AAClC,QAAMA,UAAS,cAAc,aAAa,IAAI,IAAI;AAElD,SAAO,CAAC,UAAU,CAAC,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,WAAW;AAC9B,UAAI,QAAQ,OAAO;AAClB,qBAAaA,SAAQ,MAAM;AAC1B,yBAAeA,OAAM;AACrB,iBAAO,MAAS;AAAA,QACjB,CAAC;AAAA,MACF,OAAO;AACN,uBAAeA,OAAM;AACrB,eAAO,MAAS;AAAA,MACjB;AAAA,IACD,CAAC;AAAA,EACF;AACD;AAMO,SAAS,OAAO,IAAI;AAC1B,SAAO,cAAc,QAAQ,IAAI,KAAK;AACvC;AA0DO,SAAS,cAAc,IAAI;AACjC,SAAO,cAAc,eAAe,IAAI,IAAI;AAC7C;AAgCO,SAAS,OAAO,IAAIC,QAAO,MAAM;AACvC,SAAO,cAAc,gBAAgB,eAAe,IAAI,MAAMA,KAAI;AACnE;AAKO,SAAS,wBAAwBC,SAAQ;AAC/C,MAAIC,YAAWD,QAAO;AACtB,MAAIC,cAAa,MAAM;AACtB,UAAM,+BAA+B;AACrC,UAAM,oBAAoB;AAC1B,6BAAyB,IAAI;AAC7B,wBAAoB,IAAI;AACxB,QAAI;AACH,MAAAA,UAAS,KAAK,IAAI;AAAA,IACnB,UAAE;AACD,+BAAyB,4BAA4B;AACrD,0BAAoB,iBAAiB;AAAA,IACtC;AAAA,EACD;AACD;AAOO,SAAS,wBAAwB,QAAQ,aAAa,OAAO;AACnE,MAAID,UAAS,OAAO;AACpB,SAAO,QAAQ,OAAO,OAAO;AAE7B,SAAOA,YAAW,MAAM;AACvB,QAAIE,QAAOF,QAAO;AAElB,SAAKA,QAAO,IAAI,iBAAiB,GAAG;AAEnC,MAAAA,QAAO,SAAS;AAAA,IACjB,OAAO;AACN,qBAAeA,SAAQ,UAAU;AAAA,IAClC;AAEA,IAAAA,UAASE;AAAA,EACV;AACD;AAMO,SAAS,8BAA8B,QAAQ;AACrD,MAAIF,UAAS,OAAO;AAEpB,SAAOA,YAAW,MAAM;AACvB,QAAIE,QAAOF,QAAO;AAClB,SAAKA,QAAO,IAAI,mBAAmB,GAAG;AACrC,qBAAeA,OAAM;AAAA,IACtB;AACA,IAAAA,UAASE;AAAA,EACV;AACD;AAOO,SAAS,eAAeF,SAAQ,aAAa,MAAM;AACzD,MAAI,UAAU;AAEd,OAAK,eAAeA,QAAO,IAAI,iBAAiB,MAAMA,QAAO,gBAAgB,MAAM;AAClF;AAAA,MAAkBA,QAAO;AAAA;AAAA,MAA0CA,QAAO;AAAA,IAAU;AACpF,cAAU;AAAA,EACX;AAEA,0BAAwBA,SAAQ,cAAc,CAAC,OAAO;AACtD,mBAAiBA,SAAQ,CAAC;AAC1B,oBAAkBA,SAAQ,SAAS;AAEnC,MAAI,cAAcA,QAAO;AAEzB,MAAI,gBAAgB,MAAM;AACzB,eAAWG,eAAc,aAAa;AACrC,MAAAA,YAAW,KAAK;AAAA,IACjB;AAAA,EACD;AAEA,0BAAwBH,OAAM;AAE9B,MAAI,SAASA,QAAO;AAGpB,MAAI,WAAW,QAAQ,OAAO,UAAU,MAAM;AAC7C,kBAAcA,OAAM;AAAA,EACrB;AAEA,MAAI,cAAK;AACR,IAAAA,QAAO,qBAAqB;AAAA,EAC7B;AAIA,EAAAA,QAAO,OACNA,QAAO,OACPA,QAAO,WACPA,QAAO,MACPA,QAAO,OACPA,QAAO,KACPA,QAAO,cACPA,QAAO,YACN;AACH;AAOO,SAAS,kBAAkB,MAAM,KAAK;AAC5C,SAAO,SAAS,MAAM;AAErB,QAAIE,QAAO,SAAS,MAAM;AAAA;AAAA,MAAoC,iBAAiB,IAAI;AAAA;AAEnF,SAAK,OAAO;AACZ,WAAOA;AAAA,EACR;AACD;AAOO,SAAS,cAAcF,SAAQ;AACrC,MAAI,SAASA,QAAO;AACpB,MAAI,OAAOA,QAAO;AAClB,MAAIE,QAAOF,QAAO;AAElB,MAAI,SAAS,KAAM,MAAK,OAAOE;AAC/B,MAAIA,UAAS,KAAM,CAAAA,MAAK,OAAO;AAE/B,MAAI,WAAW,MAAM;AACpB,QAAI,OAAO,UAAUF,QAAQ,QAAO,QAAQE;AAC5C,QAAI,OAAO,SAASF,QAAQ,QAAO,OAAO;AAAA,EAC3C;AACD;AAWO,SAAS,aAAaA,SAAQ,UAAU;AAE9C,MAAI,cAAc,CAAC;AAEnB,iBAAeA,SAAQ,aAAa,IAAI;AAExC,sBAAoB,aAAa,MAAM;AACtC,mBAAeA,OAAM;AACrB,QAAI,SAAU,UAAS;AAAA,EACxB,CAAC;AACF;AAMO,SAAS,oBAAoB,aAAa,IAAI;AACpD,MAAI,YAAY,YAAY;AAC5B,MAAI,YAAY,GAAG;AAClB,QAAI,QAAQ,MAAM,EAAE,aAAa,GAAG;AACpC,aAASG,eAAc,aAAa;AACnC,MAAAA,YAAW,IAAI,KAAK;AAAA,IACrB;AAAA,EACD,OAAO;AACN,OAAG;AAAA,EACJ;AACD;AAOO,SAAS,eAAeH,SAAQ,aAAa,OAAO;AAC1D,OAAKA,QAAO,IAAI,WAAW,EAAG;AAC9B,EAAAA,QAAO,KAAK;AAEZ,MAAIA,QAAO,gBAAgB,MAAM;AAChC,eAAWG,eAAcH,QAAO,aAAa;AAC5C,UAAIG,YAAW,aAAa,OAAO;AAClC,oBAAY,KAAKA,WAAU;AAAA,MAC5B;AAAA,IACD;AAAA,EACD;AAEA,MAAIC,SAAQJ,QAAO;AAEnB,SAAOI,WAAU,MAAM;AACtB,QAAIC,WAAUD,OAAM;AACpB,QAAI,eAAeA,OAAM,IAAI,wBAAwB,MAAMA,OAAM,IAAI,mBAAmB;AAIxF,mBAAeA,QAAO,aAAa,cAAc,QAAQ,KAAK;AAC9D,IAAAA,SAAQC;AAAA,EACT;AACD;;;ACjjBA,IAAI,cAAc,CAAC;AAGnB,IAAI,aAAa,CAAC;AAElB,SAAS,kBAAkB;AAC1B,MAAI,QAAQ;AACZ,gBAAc,CAAC;AACf,UAAQ,KAAK;AACd;AAEA,SAAS,iBAAiB;AACzB,MAAI,QAAQ;AACZ,eAAa,CAAC;AACd,UAAQ,KAAK;AACd;AA2BO,SAAS,cAAc;AAC7B,MAAI,YAAY,SAAS,GAAG;AAC3B,oBAAgB;AAAA,EACjB;AAEA,MAAI,WAAW,SAAS,GAAG;AAC1B,mBAAe;AAAA,EAChB;AACD;;;ACdA,IAAM,iBAAiB,oBAAI,QAAQ;AACnC,IAAI,oBAAoB;AAExB,IAAI,cAAc;AAGlB,IAAI,wBAAwB;AAE5B,IAAI,qBAAqB;AAElB,IAAI,uBAAuB;AAG3B,SAAS,yBAAyB,OAAO;AAC/C,yBAAuB;AACxB;AAKA,IAAI,sBAAsB,CAAC;AAG3B,IAAI,mBAAmB,CAAC;AAIjB,IAAI,kBAAkB;AAEtB,IAAI,aAAa;AAGjB,SAAS,oBAAoB,UAAU;AAC7C,oBAAkB;AACnB;AAGO,IAAI,gBAAgB;AAGpB,SAAS,kBAAkBC,SAAQ;AACzC,kBAAgBA;AACjB;AAOO,IAAI,mBAAmB;AAGvB,SAAS,oBAAoB,OAAO;AAC1C,MAAI,oBAAoB,QAAQ,gBAAgB,IAAI,oBAAoB;AACvE,QAAI,qBAAqB,MAAM;AAC9B,yBAAmB,CAAC,KAAK;AAAA,IAC1B,OAAO;AACN,uBAAiB,KAAK,KAAK;AAAA,IAC5B;AAAA,EACD;AACD;AAQA,IAAI,WAAW;AAEf,IAAI,eAAe;AAOZ,IAAI,mBAAmB;AAGvB,SAAS,qBAAqB,OAAO;AAC3C,qBAAmB;AACpB;AAMA,IAAI,gBAAgB;AAGpB,IAAI,eAAe;AAIZ,IAAI,gBAAgB;AAGpB,IAAI,mBAAmB;AAOvB,SAAS,0BAA0B;AACzC,SAAO,EAAE;AACV;AAQO,SAAS,gBAAgB,UAAU;AAhK1C;AAiKC,MAAI,QAAQ,SAAS;AAErB,OAAK,QAAQ,WAAW,GAAG;AAC1B,WAAO;AAAA,EACR;AAEA,OAAK,QAAQ,iBAAiB,GAAG;AAChC,QAAI,eAAe,SAAS;AAC5B,QAAI,cAAc,QAAQ,aAAa;AAEvC,QAAI,iBAAiB,MAAM;AAC1B,UAAI;AACJ,UAAI;AACJ,UAAI,mBAAmB,QAAQ,kBAAkB;AACjD,UAAI,uBAAuB,cAAc,kBAAkB,QAAQ,CAAC;AACpE,UAAI,SAAS,aAAa;AAI1B,UAAI,mBAAmB,sBAAsB;AAC5C,YAAIC;AAAA;AAAA,UAAkC;AAAA;AACtC,YAAI,SAASA,SAAQ;AAErB,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC5B,uBAAa,aAAa,CAAC;AAK3B,cAAI,mBAAmB,GAAC,8CAAY,cAAZ,mBAAuB,SAASA,YAAU;AACjE,aAAC,WAAW,cAAX,WAAW,YAAc,CAAC,IAAG,KAAKA,QAAO;AAAA,UAC3C;AAAA,QACD;AAEA,YAAI,iBAAiB;AACpB,UAAAA,SAAQ,KAAK;AAAA,QACd;AAIA,YAAI,wBAAwB,WAAW,SAAS,OAAO,IAAI,aAAa,GAAG;AAC1E,UAAAA,SAAQ,KAAK;AAAA,QACd;AAAA,MACD;AAEA,WAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC5B,qBAAa,aAAa,CAAC;AAE3B,YAAI;AAAA;AAAA,UAAwC;AAAA,QAAW,GAAG;AACzD;AAAA;AAAA,YAAuC;AAAA,UAAW;AAAA,QACnD;AAEA,YAAI,WAAW,KAAK,SAAS,IAAI;AAChC,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAIA,QAAI,CAAC,cAAe,kBAAkB,QAAQ,CAAC,eAAgB;AAC9D,wBAAkB,UAAU,KAAK;AAAA,IAClC;AAAA,EACD;AAEA,SAAO;AACR;AAMA,SAAS,gBAAgB,OAAOC,SAAQ;AAEvC,MAAI,UAAUA;AAEd,SAAO,YAAY,MAAM;AACxB,SAAK,QAAQ,IAAI,qBAAqB,GAAG;AACxC,UAAI;AAEH,gBAAQ,GAAG,KAAK;AAChB;AAAA,MACD,QAAQ;AAEP,gBAAQ,KAAK;AAAA,MACd;AAAA,IACD;AAEA,cAAU,QAAQ;AAAA,EACnB;AAEA,sBAAoB;AACpB,QAAM;AACP;AAKA,SAAS,qBAAqBA,SAAQ;AACrC,UACEA,QAAO,IAAI,eAAe,MAC1BA,QAAO,WAAW,SAASA,QAAO,OAAO,IAAI,qBAAqB;AAErE;AAYO,SAAS,aAAa,OAAOC,SAAQ,iBAAiBC,oBAAmB;AApRhF;AAqRC,MAAI,mBAAmB;AACtB,QAAI,oBAAoB,MAAM;AAC7B,0BAAoB;AAAA,IACrB;AAEA,QAAI,qBAAqBD,OAAM,GAAG;AACjC,YAAM;AAAA,IACP;AAEA;AAAA,EACD;AAEA,MAAI,oBAAoB,MAAM;AAC7B,wBAAoB;AAAA,EACrB;AAEA,MAAI,gBAAOC,uBAAsB,QAAQ,iBAAiB,SAAS,CAAC,eAAe,IAAI,KAAK,GAAG;AAC9F,mBAAe,IAAI,KAAK;AAExB,UAAM,kBAAkB,CAAC;AAEzB,UAAM,eAAc,KAAAD,QAAO,OAAP,mBAAW;AAE/B,QAAI,aAAa;AAChB,sBAAgB,KAAK,WAAW;AAAA,IACjC;AAGA,QAAI,kBAAkBC;AAEtB,WAAO,oBAAoB,MAAM;AAEhC,UAAI,YAAW,qBAAgB,aAAhB,mBAA2B;AAE1C,UAAI,UAAU;AACb,cAAM,OAAO,SAAS,MAAM,GAAG,EAAE,IAAI;AACrC,wBAAgB,KAAK,IAAI;AAAA,MAC1B;AAEA,wBAAkB,gBAAgB;AAAA,IACnC;AAEA,UAAM,SAAS,aAAa,OAAO;AACnC,oBAAgB,OAAO,WAAW;AAAA,MACjC,OACC,MAAM,UAAU;AAAA,EAAK,gBAAgB,IAAI,CAAC,SAAS;AAAA,EAAK,MAAM,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;AAAA;AAAA,IACtF,CAAC;AACD,oBAAgB,OAAO,mBAAmB;AAAA,MACzC,OAAO;AAAA,IACR,CAAC;AAED,UAAMC,SAAQ,MAAM;AAGpB,QAAIA,QAAO;AACV,YAAM,QAAQA,OAAM,MAAM,IAAI;AAC9B,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,cAAM,OAAO,MAAM,CAAC;AACpB,YAAI,KAAK,SAAS,qBAAqB,GAAG;AACzC;AAAA,QACD;AACA,kBAAU,KAAK,IAAI;AAAA,MACpB;AACA,sBAAgB,OAAO,SAAS;AAAA,QAC/B,OAAO,UAAU,KAAK,IAAI;AAAA,MAC3B,CAAC;AAAA,IACF;AAAA,EACD;AAEA,kBAAgB,OAAOF,OAAM;AAE7B,MAAI,qBAAqBA,OAAM,GAAG;AACjC,UAAM;AAAA,EACP;AACD;AAOA,SAAS,2CAA2C,QAAQA,SAAQ,OAAO,MAAM;AAChF,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc,KAAM;AAExB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,QAAI,WAAW,UAAU,CAAC;AAE1B,QAAI,qDAAkB,SAAS,QAAS;AAExC,SAAK,SAAS,IAAI,aAAa,GAAG;AACjC;AAAA;AAAA,QAAmE;AAAA,QAAWA;AAAA,QAAQ;AAAA,MAAK;AAAA,IAC5F,WAAWA,YAAW,UAAU;AAC/B,UAAI,MAAM;AACT,0BAAkB,UAAU,KAAK;AAAA,MAClC,YAAY,SAAS,IAAI,WAAW,GAAG;AACtC,0BAAkB,UAAU,WAAW;AAAA,MACxC;AACA;AAAA;AAAA,QAAuC;AAAA,MAAS;AAAA,IACjD;AAAA,EACD;AACD;AAOO,SAAS,gBAAgB,UAAU;AAlY1C;AAmYC,MAAI,gBAAgB;AACpB,MAAI,wBAAwB;AAC5B,MAAI,4BAA4B;AAChC,MAAI,oBAAoB;AACxB,MAAI,yBAAyB;AAC7B,MAAI,4BAA4B;AAChC,MAAI,6BAA6B;AACjC,MAAI,sBAAsB;AAE1B,MAAI,QAAQ,SAAS;AAErB;AAAA,EAA0C;AAC1C,iBAAe;AACf,qBAAmB;AACnB,mBACE,QAAQ,aAAa,MAAM,cAAc,CAAC,sBAAsB,oBAAoB;AACtF,qBAAmB,SAAS,gBAAgB,kBAAkB,IAAI,WAAW;AAE7E,qBAAmB;AACnB,wBAAsB,SAAS,GAAG;AAClC,eAAa;AACb;AAEA,WAAS,KAAK;AAEd,MAAI;AACH,QAAI;AAAA;AAAA,OAAkC,GAAG,SAAS,IAAI;AAAA;AACtD,QAAI,OAAO,SAAS;AAEpB,QAAI,aAAa,MAAM;AACtB,UAAI;AAEJ,uBAAiB,UAAU,YAAY;AAEvC,UAAI,SAAS,QAAQ,eAAe,GAAG;AACtC,aAAK,SAAS,eAAe,SAAS;AACtC,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACrC,eAAK,eAAe,CAAC,IAAI,SAAS,CAAC;AAAA,QACpC;AAAA,MACD,OAAO;AACN,iBAAS,OAAO,OAAO;AAAA,MACxB;AAEA,UAAI,CAAC,eAAe;AACnB,aAAK,IAAI,cAAc,IAAI,KAAK,QAAQ,KAAK;AAC5C,YAAC,UAAK,CAAC,GAAE,cAAR,GAAQ,YAAc,CAAC,IAAG,KAAK,QAAQ;AAAA,QACzC;AAAA,MACD;AAAA,IACD,WAAW,SAAS,QAAQ,eAAe,KAAK,QAAQ;AACvD,uBAAiB,UAAU,YAAY;AACvC,WAAK,SAAS;AAAA,IACf;AAKA,QACC,SAAS,KACT,qBAAqB,QACrB,CAAC,cACD,SAAS,SACR,SAAS,KAAK,UAAU,cAAc,YAAY,GAClD;AACD,WAAK,IAAI,GAAG;AAAA,MAA6B,iBAAkB,QAAQ,KAAK;AACvE;AAAA,UACC,iBAAiB,CAAC;AAAA;AAAA,UACK;AAAA,QACxB;AAAA,MACD;AAAA,IACD;AAMA,QAAI,sBAAsB,QAAQ,sBAAsB,UAAU;AACjE;AAEA,UAAI,qBAAqB,MAAM;AAC9B,YAAI,8BAA8B,MAAM;AACvC,sCAA4B;AAAA,QAC7B,OAAO;AACN,oCAA0B,KAAK;AAAA,UAA4B,gBAAiB;AAAA,QAC7E;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR,UAAE;AACD,eAAW;AACX,mBAAe;AACf,uBAAmB;AACnB,sBAAkB;AAClB,oBAAgB;AAChB,uBAAmB;AACnB,0BAAsB,0BAA0B;AAChD,iBAAa;AAEb,aAAS,KAAK;AAAA,EACf;AACD;AAQA,SAAS,gBAAgB,QAAQ,YAAY;AAC5C,MAAI,YAAY,WAAW;AAC3B,MAAI,cAAc,MAAM;AACvB,QAAIG,SAAQ,SAAS,KAAK,WAAW,MAAM;AAC3C,QAAIA,WAAU,IAAI;AACjB,UAAI,aAAa,UAAU,SAAS;AACpC,UAAI,eAAe,GAAG;AACrB,oBAAY,WAAW,YAAY;AAAA,MACpC,OAAO;AAEN,kBAAUA,MAAK,IAAI,UAAU,UAAU;AACvC,kBAAU,IAAI;AAAA,MACf;AAAA,IACD;AAAA,EACD;AAGA,MACC,cAAc,SACb,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA,GAI5B,aAAa,QAAQ,CAAC,SAAS,SAAS,UAAU,IAClD;AACD,sBAAkB,YAAY,WAAW;AAGzC,SAAK,WAAW,KAAK,UAAU,mBAAmB,GAAG;AACpD,iBAAW,KAAK;AAAA,IACjB;AAEA;AAAA;AAAA,MAAiD;AAAA,IAAW;AAC5D;AAAA;AAAA,MAA0C;AAAA,MAAa;AAAA,IAAC;AAAA,EACzD;AACD;AAOO,SAAS,iBAAiB,QAAQ,aAAa;AACrD,MAAI,eAAe,OAAO;AAC1B,MAAI,iBAAiB,KAAM;AAE3B,WAAS,IAAI,aAAa,IAAI,aAAa,QAAQ,KAAK;AACvD,oBAAgB,QAAQ,aAAa,CAAC,CAAC;AAAA,EACxC;AACD;AAMO,SAAS,cAAcH,SAAQ;AACrC,MAAI,QAAQA,QAAO;AAEnB,OAAK,QAAQ,eAAe,GAAG;AAC9B;AAAA,EACD;AAEA,oBAAkBA,SAAQ,KAAK;AAE/B,MAAI,kBAAkB;AACtB,MAAI,6BAA6B;AACjC,MAAI,sBAAsB;AAE1B,kBAAgBA;AAChB,uBAAqB;AAErB,MAAI,cAAK;AACR,QAAI,wBAAwB;AAC5B,uCAAmCA,QAAO,kBAAkB;AAAA,EAC7D;AAEA,MAAI;AACH,SAAK,QAAQ,kBAAkB,GAAG;AACjC,oCAA8BA,OAAM;AAAA,IACrC,OAAO;AACN,8BAAwBA,OAAM;AAAA,IAC/B;AAEA,4BAAwBA,OAAM;AAC9B,QAAII,YAAW,gBAAgBJ,OAAM;AACrC,IAAAA,QAAO,WAAW,OAAOI,cAAa,aAAaA,YAAW;AAC9D,IAAAJ,QAAO,KAAK;AAEZ,QAAI,OAAOA,QAAO;AAMlB,QAAI,gBAAO,sBAAsBA,QAAO,IAAI,WAAW,KAAK,SAAS,MAAM;AAC1E,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,IAAI,qBAAqB;AAC5B,cAAI,KAAK,wBAAwB;AACjC,cAAI,sBAAsB;AAC1B,cAAI,UAAU;AAAA,QACf;AAAA,MACD;AAAA,IACD;AAEA,QAAI,cAAK;AACR,uBAAiB,KAAKA,OAAM;AAAA,IAC7B;AAAA,EACD,SAAS,OAAO;AACf,iBAAa,OAAOA,SAAQ,iBAAiB,8BAA8BA,QAAO,GAAG;AAAA,EACtF,UAAE;AACD,yBAAqB;AACrB,oBAAgB;AAEhB,QAAI,cAAK;AACR,yCAAmC,qBAAqB;AAAA,IACzD;AAAA,EACD;AACD;AAEA,SAAS,mBAAmB;AAE3B,UAAQ;AAAA,IACP;AAAA,IACA,iBAAiB,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE;AAAA,EAC5C;AACA,qBAAmB,CAAC;AACrB;AAEA,SAAS,sBAAsB;AAC9B,MAAI;AACH,IAAE,6BAA6B;AAAA,EAChC,SAAS,OAAO;AACf,QAAI,cAAK;AAER,sBAAgB,OAAO,SAAS;AAAA,QAC/B,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAGA,QAAI,0BAA0B,MAAM;AACnC,UAAI,cAAK;AACR,YAAI;AACH,uBAAa,OAAO,uBAAuB,MAAM,IAAI;AAAA,QACtD,SAAS,GAAG;AAEX,2BAAiB;AACjB,gBAAM;AAAA,QACP;AAAA,MACD,OAAO;AACN,qBAAa,OAAO,uBAAuB,MAAM,IAAI;AAAA,MACtD;AAAA,IACD,OAAO;AACN,UAAI,cAAK;AACR,yBAAiB;AAAA,MAClB;AACA,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEA,SAAS,4BAA4B;AACpC,MAAI,sBAAsB;AAE1B,MAAI;AACH,QAAI,cAAc;AAClB,yBAAqB;AAErB,WAAO,oBAAoB,SAAS,GAAG;AACtC,UAAI,gBAAgB,KAAM;AACzB,4BAAoB;AAAA,MACrB;AAEA,UAAI,eAAe;AACnB,UAAI,SAAS,aAAa;AAE1B,4BAAsB,CAAC;AAEvB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,YAAI,oBAAoB,gBAAgB,aAAa,CAAC,CAAC;AACvD,6BAAqB,iBAAiB;AAAA,MACvC;AACA,iBAAW,MAAM;AAAA,IAClB;AAAA,EACD,UAAE;AACD,kBAAc;AACd,yBAAqB;AAErB,4BAAwB;AACxB,QAAI,cAAK;AACR,yBAAmB,CAAC;AAAA,IACrB;AAAA,EACD;AACD;AAMA,SAAS,qBAAqB,SAAS;AACtC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAW,EAAG;AAElB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,QAAIA,UAAS,QAAQ,CAAC;AAEtB,SAAKA,QAAO,KAAK,YAAY,YAAY,GAAG;AAC3C,UAAI;AACH,YAAI,gBAAgBA,OAAM,GAAG;AAC5B,wBAAcA,OAAM;AAOpB,cAAIA,QAAO,SAAS,QAAQA,QAAO,UAAU,QAAQA,QAAO,gBAAgB,MAAM;AACjF,gBAAIA,QAAO,aAAa,MAAM;AAE7B,4BAAcA,OAAM;AAAA,YACrB,OAAO;AAEN,cAAAA,QAAO,KAAK;AAAA,YACb;AAAA,UACD;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,qBAAa,OAAOA,SAAQ,MAAMA,QAAO,GAAG;AAAA,MAC7C;AAAA,IACD;AAAA,EACD;AACD;AAMO,SAAS,gBAAgB,QAAQ;AACvC,MAAI,CAAC,aAAa;AACjB,kBAAc;AACd,mBAAe,yBAAyB;AAAA,EACzC;AAEA,MAAIA,UAAU,wBAAwB;AAEtC,SAAOA,QAAO,WAAW,MAAM;AAC9B,IAAAA,UAASA,QAAO;AAChB,QAAI,QAAQA,QAAO;AAEnB,SAAK,SAAS,cAAc,oBAAoB,GAAG;AAClD,WAAK,QAAQ,WAAW,EAAG;AAC3B,MAAAA,QAAO,KAAK;AAAA,IACb;AAAA,EACD;AAEA,sBAAoB,KAAKA,OAAM;AAChC;AAYA,SAAS,gBAAgB,MAAM;AAE9B,MAAI,UAAU,CAAC;AAGf,MAAIA,UAAS;AAEb,SAAOA,YAAW,MAAM;AACvB,QAAI,QAAQA,QAAO;AACnB,QAAI,aAAa,SAAS,gBAAgB,kBAAkB;AAC5D,QAAI,sBAAsB,cAAc,QAAQ,WAAW;AAE3D,QAAI,CAAC,wBAAwB,QAAQ,WAAW,GAAG;AAClD,WAAK,QAAQ,YAAY,GAAG;AAC3B,gBAAQ,KAAKA,OAAM;AAAA,MACpB,WAAW,WAAW;AACrB,QAAAA,QAAO,KAAK;AAAA,MACb,OAAO;AACN,YAAI;AACH,cAAI,gBAAgBA,OAAM,GAAG;AAC5B,0BAAcA,OAAM;AAAA,UACrB;AAAA,QACD,SAAS,OAAO;AACf,uBAAa,OAAOA,SAAQ,MAAMA,QAAO,GAAG;AAAA,QAC7C;AAAA,MACD;AAGA,UAAIK,SAAQL,QAAO;AAEnB,UAAIK,WAAU,MAAM;AACnB,QAAAL,UAASK;AACT;AAAA,MACD;AAAA,IACD;AAEA,QAAI,SAASL,QAAO;AACpB,IAAAA,UAASA,QAAO;AAEhB,WAAOA,YAAW,QAAQ,WAAW,MAAM;AAC1C,MAAAA,UAAS,OAAO;AAChB,eAAS,OAAO;AAAA,IACjB;AAAA,EACD;AAEA,SAAO;AACR;AASO,SAAS,UAAU,IAAI;AAC7B,MAAI;AAEJ,MAAI,IAAI;AACP,kBAAc;AACd,8BAA0B;AAE1B,kBAAc;AACd,aAAS,GAAG;AAAA,EACb;AAEA,SAAO,MAAM;AACZ,gBAAY;AAEZ,QAAI,oBAAoB,WAAW,GAAG;AACrC;AAAA;AAAA,QAAyB;AAAA;AAAA,IAC1B;AAEA,kBAAc;AACd,8BAA0B;AAAA,EAC3B;AACD;AAMA,eAAsB,OAAO;AAC5B,QAAM,QAAQ,QAAQ;AAGtB,YAAU;AACX;AAOO,SAAS,IAAI,QAAQ;AAC3B,MAAI,QAAQ,OAAO;AACnB,MAAI,cAAc,QAAQ,aAAa;AAEvC,MAAI,qBAAqB,MAAM;AAC9B,qBAAiB,IAAI,MAAM;AAAA,EAC5B;AAGA,MAAI,oBAAoB,QAAQ,CAAC,YAAY;AAC5C,QAAI,EAAC,qDAAkB,SAAS,UAAS;AACxC,UAAI,OAAO,gBAAgB;AAC3B,UAAI,OAAO,KAAK,cAAc;AAC7B,eAAO,KAAK;AAIZ,YAAI,aAAa,QAAQ,SAAS,QAAQ,KAAK,YAAY,MAAM,QAAQ;AACxE;AAAA,QACD,WAAW,aAAa,MAAM;AAC7B,qBAAW,CAAC,MAAM;AAAA,QACnB,WAAW,CAAC,iBAAiB,CAAC,SAAS,SAAS,MAAM,GAAG;AAIxD,mBAAS,KAAK,MAAM;AAAA,QACrB;AAAA,MACD;AAAA,IACD;AAAA,EACD,WACC;AAAA,EACwB,OAAQ,SAAS;AAAA,EACjB,OAAQ,YAAY,MAC3C;AACD,QAAIM;AAAA;AAAA,MAAkC;AAAA;AACtC,QAAI,SAASA,SAAQ;AAErB,QAAI,WAAW,SAAS,OAAO,IAAI,aAAa,GAAG;AAIlD,MAAAA,SAAQ,KAAK;AAAA,IACd;AAAA,EACD;AAEA,MAAI,YAAY;AACf,IAAAA;AAAA,IAAkC;AAElC,QAAI,gBAAgBA,QAAO,GAAG;AAC7B,qBAAeA,QAAO;AAAA,IACvB;AAAA,EACD;AAEA,MACC,gBACA,qBACA,wBAAwB,QACxB,oBAAoB,QACpB,oBAAoB,aAAa,iBAChC;AAED,QAAI,OAAO,OAAO;AACjB,aAAO,MAAM;AAAA,IACd,WAAW,OAAO,SAAS;AAC1B,UAAI,QAAQ,oBAAoB,QAAQ,IAAI,MAAM;AAElD,UAAI,UAAU,QAAW;AACxB,gBAAQ,EAAE,MAAM,CAAC,EAAE;AACnB,4BAAoB,QAAQ,IAAI,QAAQ,KAAK;AAAA,MAC9C;AAEA,YAAM,KAAK,KAAK,UAAU,UAAU,CAAC;AAAA,IACtC;AAAA,EACD;AAEA,MAAI,wBAAwB,WAAW,IAAI,MAAM,GAAG;AACnD,WAAO,WAAW,IAAI,MAAM;AAAA,EAC7B;AAEA,SAAO,OAAO;AACf;AA6EO,SAAS,QAAQ,IAAI;AAC3B,MAAI,sBAAsB;AAC1B,MAAI;AACH,iBAAa;AACb,WAAO,GAAG;AAAA,EACX,UAAE;AACD,iBAAa;AAAA,EACd;AACD;AAEA,IAAM,cAAc,EAAE,QAAQ,cAAc;AAOrC,SAAS,kBAAkB,QAAQ,QAAQ;AACjD,SAAO,IAAK,OAAO,IAAI,cAAe;AACvC;;;ACx3BA,IAAM,yBAAyB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAwCA,IAAM,iBAAiB;AAAA,EACtB,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AA6BA,IAAM,iBAAiB,CAAC,cAAc,WAAW;AAM1C,SAAS,iBAAiB,MAAM;AACtC,SAAO,eAAe,SAAS,IAAI;AACpC;AAiKO,IAAM;AAAA;AAAA,EAA6C;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAEA,IAAM;AAAA;AAAA,EAA8B;AAAA,IACnC,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;;;ACpbO,IAAM,wBAAwB,oBAAI,IAAI;AAGtC,IAAM,qBAAqB,oBAAI,IAAI;AAkInC,SAAS,yBAAyBC,QAAO;AApJhD;AAqJC,MAAI,kBAAkB;AACtB,MAAI;AAAA;AAAA,IAAsC,gBAAiB;AAAA;AAC3D,MAAI,aAAaA,OAAM;AACvB,MAAI,SAAO,KAAAA,OAAM,iBAAN,wBAAAA,YAA0B,CAAC;AACtC,MAAI;AAAA;AAAA,IAAgD,KAAK,CAAC,KAAKA,OAAM;AAAA;AAMrE,MAAI,WAAW;AAGf,MAAI,aAAaA,OAAM;AAEvB,MAAI,YAAY;AACf,QAAI,SAAS,KAAK,QAAQ,UAAU;AACpC,QACC,WAAW,OACV,oBAAoB,YAAY;AAAA,IAAwC,SACxE;AAKD,MAAAA,OAAM,SAAS;AACf;AAAA,IACD;AAOA,QAAI,cAAc,KAAK,QAAQ,eAAe;AAC9C,QAAI,gBAAgB,IAAI;AAGvB;AAAA,IACD;AAEA,QAAI,UAAU,aAAa;AAC1B,iBAAW;AAAA,IACZ;AAAA,EACD;AAEA;AAAA,EAAyC,KAAK,QAAQ,KAAKA,OAAM;AAIjE,MAAI,mBAAmB,gBAAiB;AAGxC,kBAAgBA,QAAO,iBAAiB;AAAA,IACvC,cAAc;AAAA,IACd,MAAM;AACL,aAAO,kBAAkB;AAAA,IAC1B;AAAA,EACD,CAAC;AAOD,MAAI,oBAAoB;AACxB,MAAI,kBAAkB;AACtB,sBAAoB,IAAI;AACxB,oBAAkB,IAAI;AAEtB,MAAI;AAIH,QAAI;AAIJ,QAAI,eAAe,CAAC;AAEpB,WAAO,mBAAmB,MAAM;AAE/B,UAAI,iBACH,eAAe,gBACf,eAAe;AAAA,MACK,eAAgB,QACpC;AAED,UAAI;AAEH,YAAI,YAAY,eAAe,OAAO,UAAU;AAEhD,YACC,aAAa,SACZ;AAAA,QAAsB,eAAgB;AAAA;AAAA,QAGtCA,OAAM,WAAW,iBACjB;AACD,cAAI,SAAS,SAAS,GAAG;AACxB,gBAAI,CAAC,IAAI,GAAG,IAAI,IAAI;AACpB,eAAG,MAAM,gBAAgB,CAACA,QAAO,GAAG,IAAI,CAAC;AAAA,UAC1C,OAAO;AACN,sBAAU,KAAK,gBAAgBA,MAAK;AAAA,UACrC;AAAA,QACD;AAAA,MACD,SAAS,OAAO;AACf,YAAI,aAAa;AAChB,uBAAa,KAAK,KAAK;AAAA,QACxB,OAAO;AACN,wBAAc;AAAA,QACf;AAAA,MACD;AACA,UAAIA,OAAM,gBAAgB,mBAAmB,mBAAmB,mBAAmB,MAAM;AACxF;AAAA,MACD;AACA,uBAAiB;AAAA,IAClB;AAEA,QAAI,aAAa;AAChB,eAAS,SAAS,cAAc;AAE/B,uBAAe,MAAM;AACpB,gBAAM;AAAA,QACP,CAAC;AAAA,MACF;AACA,YAAM;AAAA,IACP;AAAA,EACD,UAAE;AAED,IAAAA,OAAM,SAAS;AAEf,WAAOA,OAAM;AACb,wBAAoB,iBAAiB;AACrC,sBAAkB,eAAe;AAAA,EAClC;AACD;;;ACnRA,IAAI;AAEG,SAAS,oBAAoB;AACnC,gBAAc;AACf;;;ACbO,SAAS,0BAA0BC,OAAM;AAC/C,MAAI,OAAO,SAAS,cAAc,UAAU;AAC5C,OAAK,YAAYA,MAAK,WAAW,OAAO,SAAS;AACjD,SAAO,KAAK;AACb;;;ACsBO,SAAS,aAAa,OAAO,KAAK;AACxC,MAAIC;AAAA;AAAA,IAAgC;AAAA;AACpC,MAAIA,QAAO,gBAAgB,MAAM;AAChC,IAAAA,QAAO,cAAc;AACrB,IAAAA,QAAO,YAAY;AAAA,EACpB;AACD;AAmTO,SAAS,OAAO,QAAQ,KAAK;AACnC,MAAI,WAAW;AACQ,IAAC,cAAe,YAAY;AAClD,iBAAa;AACb;AAAA,EACD;AAEA,MAAI,WAAW,MAAM;AAEpB;AAAA,EACD;AAEA,SAAO;AAAA;AAAA,IAA4B;AAAA,EAAI;AACxC;;;AC3TO,IAAI,eAAe;AAiCnB,SAAS,MAAMC,YAAW,SAAS;AACzC,SAAO,OAAOA,YAAW,OAAO;AACjC;AAyBO,SAAS,QAAQA,YAAW,SAAS;AAC3C,kBAAgB;AAChB,UAAQ,QAAQ,QAAQ,SAAS;AACjC,QAAM,SAAS,QAAQ;AACvB,QAAM,gBAAgB;AACtB,QAAM,wBAAwB;AAE9B,MAAI;AACH,QAAI;AAAA;AAAA,MAAsC,gBAAgB,MAAM;AAAA;AAChE,WACC,WACC,OAAO,aAAa;AAAA,IAA6B,OAAQ,SAAS,kBAClE;AACD;AAAA,MAAsC,iBAAiB,MAAM;AAAA,IAC9D;AAEA,QAAI,CAAC,QAAQ;AACZ,YAAM;AAAA,IACP;AAEA,kBAAc,IAAI;AAClB;AAAA;AAAA,MAAyC;AAAA,IAAO;AAChD,iBAAa;AAEb,UAAM,WAAW,OAAOA,YAAW,EAAE,GAAG,SAAS,OAAO,CAAC;AAEzD,QACC,iBAAiB,QACjB,aAAa,aAAa;AAAA,IACF,aAAc,SAAS,eAC9C;AACD,MAAE,mBAAmB;AACrB,YAAM;AAAA,IACP;AAEA,kBAAc,KAAK;AAEnB;AAAA;AAAA,MAAgC;AAAA;AAAA,EACjC,SAAS,OAAO;AACf,QAAI,UAAU,iBAAiB;AAC9B,UAAI,QAAQ,YAAY,OAAO;AAC9B,QAAE,iBAAiB;AAAA,MACpB;AAGA,sBAAgB;AAChB,yBAAmB,MAAM;AAEzB,oBAAc,KAAK;AACnB,aAAO,MAAMA,YAAW,OAAO;AAAA,IAChC;AAEA,UAAM;AAAA,EACP,UAAE;AACD,kBAAc,aAAa;AAC3B,qBAAiB,qBAAqB;AACtC,sBAAkB;AAAA,EACnB;AACD;AAGA,IAAM,qBAAqB,oBAAI,IAAI;AAQnC,SAAS,OAAO,WAAW,EAAE,QAAQ,QAAQ,QAAQ,CAAC,GAAG,QAAQ,SAAS,QAAQ,KAAK,GAAG;AACzF,kBAAgB;AAEhB,MAAI,oBAAoB,oBAAI,IAAI;AAGhC,MAAI,eAAe,CAACC,YAAW;AAC9B,aAAS,IAAI,GAAG,IAAIA,QAAO,QAAQ,KAAK;AACvC,UAAI,aAAaA,QAAO,CAAC;AAEzB,UAAI,kBAAkB,IAAI,UAAU,EAAG;AACvC,wBAAkB,IAAI,UAAU;AAEhC,UAAIC,WAAU,iBAAiB,UAAU;AAKzC,aAAO,iBAAiB,YAAY,0BAA0B,EAAE,SAAAA,SAAQ,CAAC;AAEzE,UAAI,IAAI,mBAAmB,IAAI,UAAU;AAEzC,UAAI,MAAM,QAAW;AAGpB,iBAAS,iBAAiB,YAAY,0BAA0B,EAAE,SAAAA,SAAQ,CAAC;AAC3E,2BAAmB,IAAI,YAAY,CAAC;AAAA,MACrC,OAAO;AACN,2BAAmB,IAAI,YAAY,IAAI,CAAC;AAAA,MACzC;AAAA,IACD;AAAA,EACD;AAEA,eAAa,WAAW,qBAAqB,CAAC;AAC9C,qBAAmB,IAAI,YAAY;AAInC,MAAIF,aAAY;AAEhB,MAAIG,WAAU,eAAe,MAAM;AAClC,QAAI,cAAc,UAAU,OAAO,YAAY,YAAY,CAAC;AAE5D,WAAO,MAAM;AACZ,UAAI,SAAS;AACZ,aAAK,CAAC,CAAC;AACP,YAAI;AAAA;AAAA,UAAuC;AAAA;AAC3C,YAAI,IAAI;AAAA,MACT;AAEA,UAAI,QAAQ;AAEQ,QAAC,MAAO,WAAW;AAAA,MACvC;AAEA,UAAI,WAAW;AACd;AAAA;AAAA,UAA0C;AAAA,UAAc;AAAA,QAAI;AAAA,MAC7D;AAEA,qBAAe;AAEf,MAAAH,aAAY,UAAU,aAAa,KAAK,KAAK,CAAC;AAC9C,qBAAe;AAEf,UAAI,WAAW;AACQ,QAAC,cAAe,YAAY;AAAA,MACnD;AAEA,UAAI,SAAS;AACZ,YAAI;AAAA,MACL;AAAA,IACD,CAAC;AAED,WAAO,MAAM;AAhPf;AAiPG,eAAS,cAAc,mBAAmB;AACzC,eAAO,oBAAoB,YAAY,wBAAwB;AAE/D,YAAI;AAAA;AAAA,UAA2B,mBAAmB,IAAI,UAAU;AAAA;AAEhE,YAAI,EAAE,MAAM,GAAG;AACd,mBAAS,oBAAoB,YAAY,wBAAwB;AACjE,6BAAmB,OAAO,UAAU;AAAA,QACrC,OAAO;AACN,6BAAmB,IAAI,YAAY,CAAC;AAAA,QACrC;AAAA,MACD;AAEA,yBAAmB,OAAO,YAAY;AAEtC,UAAI,gBAAgB,QAAQ;AAC3B,0BAAY,eAAZ,mBAAwB,YAAY;AAAA,MACrC;AAAA,IACD;AAAA,EACD,CAAC;AAED,qBAAmB,IAAIA,YAAWG,QAAO;AACzC,SAAOH;AACR;AAMA,IAAI,qBAAqB,oBAAI,QAAQ;AAsB9B,SAAS,QAAQA,YAAW,SAAS;AAC3C,QAAM,KAAK,mBAAmB,IAAIA,UAAS;AAE3C,MAAI,IAAI;AACP,uBAAmB,OAAOA,UAAS;AACnC,WAAO,GAAG,OAAO;AAAA,EAClB;AAEA,MAAI,cAAK;AACR,IAAE,yBAAyB;AAAA,EAC5B;AAEA,SAAO,QAAQ,QAAQ;AACxB;;;ACzNO,SAAS,iBAAiB,IAAI;AAEpC,SAAO,CAA6B,WAA0C,WAAW;AA1F1F;AA2FE,QAAII,WAAU,GAAG,GAAG,MAAM;AAG1B,QAAIC;AAEJ,QAAI,WAAW;AACd,MAAAA;AAAA,MAAkC;AAClC,mBAAa;AAAA,IACd,OAAO;AACN,UAAIC,QAAOF,SAAQ,OAAO,EAAE,KAAK;AACjC,UAAI,WAAW,0BAA0BE,KAAI;AAC7C,MAAAD;AAAA,MAAkC,gBAAgB,QAAQ;AAE1D,UAAI,iBAAQ,iBAAiBA,QAAO,MAAM,QAAQA,SAAQ,aAAa,IAAI;AAC1E,QAAE,2BAA2B;AAAA,MAC9B;AAEA,aAAO,OAAOA,QAAO;AAAA,IACtB;AAEA,UAAM,UAAS,KAAAD,SAAQ,UAAR,wBAAAA,UAAgBC;AAC/B,iBAAaA,UAASA,QAAO;AAE7B,QAAI,OAAO,WAAW,YAAY;AACjC,eAAS,MAAM;AAAA,IAChB;AAAA,EACD;AACD;;;AC3EA,IAAM,aAAa,CAAC,GAAG,mBAA6B;;;ACjB7C,IAAM,QAAQ,OAAO,OAAO;AAC5B,IAAM,QAAQ,OAAO,OAAO;AAEnC,IAAM,oBAAoB,OAAO,mBAAmB;AACpD,IAAM,UAAU,OAAO,SAAS;;;AC9BhC;AAQA,IAAM,2BAAN,MAAM,yBAAwB;AAAA;AAAA,EAc7B,YAAY,SAAS;AAdtB;AAEC;AAAA,mCAAa,oBAAI,QAAQ;AAGzB;AAAA;AAGA;AAAA;AAOC,uBAAK,UAAW;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQE,UAAS,UAAU;AAC1B,QAAI,YAAY,mBAAK,YAAW,IAAIA,QAAO,KAAK,oBAAI,IAAI;AACxD,cAAU,IAAI,QAAQ;AAEtB,uBAAK,YAAW,IAAIA,UAAS,SAAS;AACtC,0BAAK,oDAAL,WAAoB,QAAQA,UAAS,mBAAK,SAAQ;AAElD,WAAO,MAAM;AACZ,UAAIC,aAAY,mBAAK,YAAW,IAAID,QAAO;AAC3C,MAAAC,WAAU,OAAO,QAAQ;AAEzB,UAAIA,WAAU,SAAS,GAAG;AACzB,2BAAK,YAAW,OAAOD,QAAO;AACA,QAAC,mBAAK,WAAW,UAAUA,QAAO;AAAA,MACjE;AAAA,IACD;AAAA,EACD;AAiBD;AArDC;AAGA;AAGA;AARD;AAwCC,iBAAY,WAAG;AACd,SACC,mBAAK,cACJ,mBAAK,WAAY,IAAI;AAAA;AAAA,IACO,CAAC,YAAY;AACxC,eAAS,SAAS,SAAS;AAC1B,iCAAwB,QAAQ,IAAI,MAAM,QAAQ,KAAK;AACvD,iBAAS,YAAY,mBAAK,YAAW,IAAI,MAAM,MAAM,KAAK,CAAC,GAAG;AAC7D,mBAAS,KAAK;AAAA,QACf;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEF;AAAA;AA3CA,cAXK,0BAWE,WAAU,oBAAI,QAAQ;AAX9B,IAAM,0BAAN;AAyDA,IAAI,8BAA8C,IAAI,wBAAwB;AAAA,EAC7E,KAAK;AACN,CAAC;AAED,IAAI,6BAA6C,IAAI,wBAAwB;AAAA,EAC5E,KAAK;AACN,CAAC;AAED,IAAI,2CAA2D,IAAI,wBAAwB;AAAA,EAC1F,KAAK;AACN,CAAC;;;AC3DD,IAAI,eAAe,OAAO;;;ACYnB,SAAS,qBAAqB,SAAS;AAE7C,SAAO,IAAI,iBAAiB,OAAO;AACpC;AA/BA;AAgEA,IAAM,mBAAN,MAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYtB,YAAY,SAAS;AAVrB;AAAA;AAGA;AAAA;AArED;AA6EE,QAAI,UAAU,oBAAI,IAAI;AAMtB,QAAI,aAAa,CAAC,KAAK,UAAU;AAChC,UAAI,IAAI,eAAe,KAAK;AAC5B,cAAQ,IAAI,KAAK,CAAC;AAClB,aAAO;AAAA,IACR;AAKA,UAAM,QAAQ,IAAI;AAAA,MACjB,EAAE,GAAI,QAAQ,SAAS,CAAC,GAAI,UAAU,CAAC,EAAE;AAAA,MACzC;AAAA,QACC,IAAI,QAAQE,OAAM;AACjB,iBAAO,IAAI,QAAQ,IAAIA,KAAI,KAAK,WAAWA,OAAM,QAAQ,IAAI,QAAQA,KAAI,CAAC,CAAC;AAAA,QAC5E;AAAA,QACA,IAAI,QAAQA,OAAM;AAEjB,cAAIA,UAAS,aAAc,QAAO;AAElC,cAAI,QAAQ,IAAIA,KAAI,KAAK,WAAWA,OAAM,QAAQ,IAAI,QAAQA,KAAI,CAAC,CAAC;AACpE,iBAAO,QAAQ,IAAI,QAAQA,KAAI;AAAA,QAChC;AAAA,QACA,IAAI,QAAQA,OAAM,OAAO;AACxB,cAAI,QAAQ,IAAIA,KAAI,KAAK,WAAWA,OAAM,KAAK,GAAG,KAAK;AACvD,iBAAO,QAAQ,IAAI,QAAQA,OAAM,KAAK;AAAA,QACvC;AAAA,MACD;AAAA,IACD;AAEA,uBAAK,YAAa,QAAQ,UAAU,UAAU,OAAO,QAAQ,WAAW;AAAA,MACvE,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,MAChB;AAAA,MACA,SAAS,QAAQ;AAAA,MACjB,OAAO,QAAQ,SAAS;AAAA,MACxB,SAAS,QAAQ;AAAA,IAClB,CAAC;AAGD,QAAI,GAAC,wCAAS,UAAT,mBAAgB,WAAU,QAAQ,SAAS,OAAO;AACtD,gBAAU;AAAA,IACX;AAEA,uBAAK,SAAU,MAAM;AAErB,eAAW,OAAO,OAAO,KAAK,mBAAK,UAAS,GAAG;AAC9C,UAAI,QAAQ,UAAU,QAAQ,cAAc,QAAQ,MAAO;AAC3D,sBAAgB,MAAM,KAAK;AAAA,QAC1B,MAAM;AACL,iBAAO,mBAAK,WAAU,GAAG;AAAA,QAC1B;AAAA;AAAA,QAEA,IAAI,OAAO;AACV,6BAAK,WAAU,GAAG,IAAI;AAAA,QACvB;AAAA,QACA,YAAY;AAAA,MACb,CAAC;AAAA,IACF;AAEA,uBAAK,WAAU;AAAA,IAAgD,CAACC,UAAS;AACxE,aAAO,OAAO,OAAOA,KAAI;AAAA,IAC1B;AAEA,uBAAK,WAAU,WAAW,MAAM;AAC/B,cAAQ,mBAAK,UAAS;AAAA,IACvB;AAAA,EACD;AAAA;AAAA,EAGA,KAAK,OAAO;AACX,uBAAK,WAAU,KAAK,KAAK;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAIC,QAAO,UAAU;AACpB,uBAAK,SAAQA,MAAK,IAAI,mBAAK,SAAQA,MAAK,KAAK,CAAC;AAG9C,UAAM,KAAK,IAAI,SAAS,SAAS,KAAK,MAAM,GAAG,IAAI;AACnD,uBAAK,SAAQA,MAAK,EAAE,KAAK,EAAE;AAC3B,WAAO,MAAM;AACZ,yBAAK,SAAQA,MAAK,IAAI,mBAAK,SAAQA,MAAK,EAAE;AAAA;AAAA,QAA8B,CAAC,OAAO,OAAO;AAAA,MAAE;AAAA,IAC1F;AAAA,EACD;AAAA,EAEA,WAAW;AACV,uBAAK,WAAU,SAAS;AAAA,EACzB;AACD;AA7GC;AAGA;;;ACxDD,IAAI;AAEJ,IAAI,OAAO,gBAAgB,YAAY;AACtC,kBAAgB,cAAc,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA2BzC,YAAY,iBAAiB,SAAS,gBAAgB;AACrD,YAAM;AA1BP;AAAA;AAEA;AAAA;AAEA;AAAA;AAEA;AAAA,kCAAO;AAEP;AAAA,iCAAM,CAAC;AAEP;AAAA,iCAAM;AAEN;AAAA,mCAAQ,CAAC;AAET;AAAA,iCAAM,CAAC;AAEP;AAAA,mCAAQ,oBAAI,IAAI;AAEhB;AAAA;AASC,WAAK,SAAS;AACd,WAAK,MAAM;AACX,UAAI,gBAAgB;AACnB,aAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAAA,MACnC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,iBAAiB,MAAM,UAAU,SAAS;AAIzC,WAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC;AACpC,WAAK,IAAI,IAAI,EAAE,KAAK,QAAQ;AAC5B,UAAI,KAAK,KAAK;AACb,cAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,QAAQ;AACzC,aAAK,MAAM,IAAI,UAAU,KAAK;AAAA,MAC/B;AACA,YAAM,iBAAiB,MAAM,UAAU,OAAO;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,oBAAoB,MAAM,UAAU,SAAS;AAC5C,YAAM,oBAAoB,MAAM,UAAU,OAAO;AACjD,UAAI,KAAK,KAAK;AACb,cAAM,QAAQ,KAAK,MAAM,IAAI,QAAQ;AACrC,YAAI,OAAO;AACV,gBAAM;AACN,eAAK,MAAM,OAAO,QAAQ;AAAA,QAC3B;AAAA,MACD;AAAA,IACD;AAAA,IAEA,MAAM,oBAAoB;AACzB,WAAK,OAAO;AACZ,UAAI,CAAC,KAAK,KAAK;AAOd,YAAS,cAAT,SAAqB,MAAM;AAI1B,iBAAO,CAAC,WAAW;AAClB,kBAAMC,QAAO,SAAS,cAAc,MAAM;AAC1C,gBAAI,SAAS,UAAW,CAAAA,MAAK,OAAO;AAEpC,mBAAO,QAAQA,KAAI;AAAA,UACpB;AAAA,QACD;AAfA,cAAM,QAAQ,QAAQ;AACtB,YAAI,CAAC,KAAK,QAAQ,KAAK,KAAK;AAC3B;AAAA,QACD;AAcA,cAAM,UAAU,CAAC;AACjB,cAAM,iBAAiB,0BAA0B,IAAI;AACrD,mBAAW,QAAQ,KAAK,KAAK;AAC5B,cAAI,QAAQ,gBAAgB;AAC3B,gBAAI,SAAS,aAAa,CAAC,KAAK,IAAI,UAAU;AAC7C,mBAAK,IAAI,WAAW,YAAY,IAAI;AACpC,sBAAQ,UAAU;AAAA,YACnB,OAAO;AACN,sBAAQ,IAAI,IAAI,YAAY,IAAI;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AACA,mBAAW,aAAa,KAAK,YAAY;AAExC,gBAAM,OAAO,KAAK,MAAM,UAAU,IAAI;AACtC,cAAI,EAAE,QAAQ,KAAK,MAAM;AACxB,iBAAK,IAAI,IAAI,IAAI,yBAAyB,MAAM,UAAU,OAAO,KAAK,OAAO,QAAQ;AAAA,UACtF;AAAA,QACD;AAEA,mBAAW,OAAO,KAAK,OAAO;AAE7B,cAAI,EAAE,OAAO,KAAK,QAAQ,KAAK,GAAG,MAAM,QAAW;AAElD,iBAAK,IAAI,GAAG,IAAI,KAAK,GAAG;AAExB,mBAAO,KAAK,GAAG;AAAA,UAChB;AAAA,QACD;AACA,aAAK,MAAM,qBAAqB;AAAA,UAC/B,WAAW,KAAK;AAAA,UAChB,QAAQ,KAAK,cAAc;AAAA,UAC3B,OAAO;AAAA,YACN,GAAG,KAAK;AAAA,YACR;AAAA,YACA,QAAQ;AAAA,UACT;AAAA,QACD,CAAC;AAGD,aAAK,OAAO,YAAY,MAAM;AAC7B,wBAAc,MAAM;AApJzB;AAqJM,iBAAK,MAAM;AACX,uBAAW,OAAO,YAAY,KAAK,GAAG,GAAG;AACxC,kBAAI,GAAC,UAAK,MAAM,GAAG,MAAd,mBAAiB,SAAS;AAC/B,mBAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AAC5B,oBAAM,kBAAkB;AAAA,gBACvB;AAAA,gBACA,KAAK,IAAI,GAAG;AAAA,gBACZ,KAAK;AAAA,gBACL;AAAA,cACD;AACA,kBAAI,mBAAmB,MAAM;AAC5B,qBAAK,gBAAgB,KAAK,MAAM,GAAG,EAAE,aAAa,GAAG;AAAA,cACtD,OAAO;AACN,qBAAK,aAAa,KAAK,MAAM,GAAG,EAAE,aAAa,KAAK,eAAe;AAAA,cACpE;AAAA,YACD;AACA,iBAAK,MAAM;AAAA,UACZ,CAAC;AAAA,QACF,CAAC;AAED,mBAAW,QAAQ,KAAK,KAAK;AAC5B,qBAAW,YAAY,KAAK,IAAI,IAAI,GAAG;AACtC,kBAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,QAAQ;AACzC,iBAAK,MAAM,IAAI,UAAU,KAAK;AAAA,UAC/B;AAAA,QACD;AACA,aAAK,MAAM,CAAC;AAAA,MACb;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,yBAAyBC,OAAM,WAAW,UAAU;AA3LtD;AA4LG,UAAI,KAAK,IAAK;AACd,MAAAA,QAAO,KAAK,MAAMA,KAAI;AACtB,WAAK,IAAIA,KAAI,IAAI,yBAAyBA,OAAM,UAAU,KAAK,OAAO,QAAQ;AAC9E,iBAAK,QAAL,mBAAU,KAAK,EAAE,CAACA,KAAI,GAAG,KAAK,IAAIA,KAAI,EAAE;AAAA,IACzC;AAAA,IAEA,uBAAuB;AACtB,WAAK,OAAO;AAEZ,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC5B,YAAI,CAAC,KAAK,QAAQ,KAAK,KAAK;AAC3B,eAAK,IAAI,SAAS;AAClB,eAAK,KAAK;AACV,eAAK,MAAM;AAAA,QACZ;AAAA,MACD,CAAC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,gBAAgB;AACrB,aACC,YAAY,KAAK,KAAK,EAAE;AAAA,QACvB,CAAC,QACA,KAAK,MAAM,GAAG,EAAE,cAAc,kBAC7B,CAAC,KAAK,MAAM,GAAG,EAAE,aAAa,IAAI,YAAY,MAAM;AAAA,MACvD,KAAK;AAAA,IAEP;AAAA,EACD;AACD;AAQA,SAAS,yBAAyBC,OAAM,OAAO,kBAAkB,WAAW;AAnO5E;AAoOC,QAAM,QAAO,sBAAiBA,KAAI,MAArB,mBAAwB;AACrC,UAAQ,SAAS,aAAa,OAAO,UAAU,YAAY,SAAS,OAAO;AAC3E,MAAI,CAAC,aAAa,CAAC,iBAAiBA,KAAI,GAAG;AAC1C,WAAO;AAAA,EACR,WAAW,cAAc,eAAe;AACvC,YAAQ,MAAM;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACJ,eAAO,SAAS,OAAO,OAAO,KAAK,UAAU,KAAK;AAAA,MACnD,KAAK;AACJ,eAAO,QAAQ,KAAK;AAAA,MACrB,KAAK;AACJ,eAAO,SAAS,OAAO,OAAO;AAAA,MAC/B;AACC,eAAO;AAAA,IACT;AAAA,EACD,OAAO;AACN,YAAQ,MAAM;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACJ,eAAO,SAAS,KAAK,MAAM,KAAK;AAAA,MACjC,KAAK;AACJ,eAAO;AAAA;AAAA,MACR,KAAK;AACJ,eAAO,SAAS,OAAO,CAAC,QAAQ;AAAA,MACjC;AACC,eAAO;AAAA,IACT;AAAA,EACD;AACD;AAKA,SAAS,0BAA0BC,UAAS;AAE3C,QAAM,SAAS,CAAC;AAChB,EAAAA,SAAQ,WAAW,QAAQ,CAAC,SAAS;AACpC;AAAA;AAAA,MAAoC,KAAM,QAAQ;AAAA,IAAS,IAAI;AAAA,EAChE,CAAC;AACD,SAAO;AACR;;;ACjQA,IAAI,cAAK;AAIR,MAAS,mBAAT,SAA0B,MAAM;AAC/B,QAAI,EAAE,QAAQ,aAAa;AAG1B,UAAI;AACJ,aAAO,eAAe,YAAY,MAAM;AAAA,QACvC,cAAc;AAAA;AAAA,QAEd,KAAK,MAAM;AACV,cAAI,UAAU,QAAW;AACxB,mBAAO;AAAA,UACR;AAEA,UAAE,oBAAoB,IAAI;AAAA,QAC3B;AAAA,QACA,KAAK,CAAC,MAAM;AACX,kBAAQ;AAAA,QACT;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAEA,mBAAiB,QAAQ;AACzB,mBAAiB,SAAS;AAC1B,mBAAiB,UAAU;AAC3B,mBAAiB,UAAU;AAC3B,mBAAiB,QAAQ;AACzB,mBAAiB,WAAW;AAC7B;AAgBO,SAAS,QAAQ,IAAI;AAC3B,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,SAAS;AAAA,EACtC;AAEA,MAAI,oBAAoB,kBAAkB,MAAM,MAAM;AACrD,0BAAsB,iBAAiB,EAAE,EAAE,KAAK,EAAE;AAAA,EACnD,OAAO;AACN,gBAAY,MAAM;AACjB,YAAM,UAAU,QAAQ,EAAE;AAC1B,UAAI,OAAO,YAAY,WAAY;AAAA;AAAA,QAAkC;AAAA;AAAA,IACtE,CAAC;AAAA,EACF;AACD;AAWO,SAAS,UAAU,IAAI;AAC7B,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,WAAW;AAAA,EACxC;AAEA,UAAQ,MAAM,MAAM,QAAQ,EAAE,CAAC;AAChC;AASA,SAAS,oBAAoB,MAAM,QAAQ,EAAE,UAAU,OAAO,aAAa,MAAM,IAAI,CAAC,GAAG;AACxF,SAAO,IAAI,YAAY,MAAM,EAAE,QAAQ,SAAS,WAAW,CAAC;AAC7D;AAyBO,SAAS,wBAAwB;AACvC,QAAM,2BAA2B;AACjC,MAAI,6BAA6B,MAAM;AACtC,gCAA4B,uBAAuB;AAAA,EACpD;AAEA,SAAO,CAAC,MAAM,QAAQ,YAAY;AApInC;AAqIE,UAAM;AAAA;AAAA,OACL,8BAAyB,EAAE,aAA3B;AAAA;AAAA,QACuB;AAAA;AAAA;AAExB,QAAI,QAAQ;AACX,YAAM,YAAY,SAAS,MAAM,IAAI,OAAO,MAAM,IAAI,CAAC,MAAM;AAG7D,YAAMC,SAAQ;AAAA;AAAA,QAA2C;AAAA,QAAO;AAAA,QAAQ;AAAA,MAAO;AAC/E,iBAAW,MAAM,WAAW;AAC3B,WAAG,KAAK,yBAAyB,GAAGA,MAAK;AAAA,MAC1C;AACA,aAAO,CAACA,OAAM;AAAA,IACf;AAEA,WAAO;AAAA,EACR;AACD;AAeO,SAAS,aAAa,IAAI;AAChC,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,cAAc;AAAA,EAC3C;AAEA,MAAI,kBAAkB,MAAM,MAAM;AACjC,IAAE,sBAAsB,cAAc;AAAA,EACvC;AAEA,wBAAsB,iBAAiB,EAAE,EAAE,KAAK,EAAE;AACnD;AAaO,SAAS,YAAY,IAAI;AAC/B,MAAI,sBAAsB,MAAM;AAC/B,gCAA4B,aAAa;AAAA,EAC1C;AAEA,MAAI,kBAAkB,MAAM,MAAM;AACjC,IAAE,sBAAsB,aAAa;AAAA,EACtC;AAEA,wBAAsB,iBAAiB,EAAE,EAAE,KAAK,EAAE;AACnD;AAMA,SAAS,sBAAsB,SAAS;AACvC,MAAI;AAAA;AAAA,IAA2C,QAAS;AAAA;AACxD,SAAQ,EAAE,MAAF,EAAE,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE;AACvC;", "names": ["stack", "component", "component_context", "stack", "prop", "source", "value", "key", "derived", "stack", "source", "effect", "array_prototype", "index", "effect", "push", "derived", "effect", "effect", "push", "effect", "teardown", "next", "transition", "child", "sibling", "effect", "derived", "effect", "effect", "component_context", "stack", "index", "teardown", "child", "derived", "event", "html", "effect", "component", "events", "passive", "unmount", "snippet", "element", "html", "element", "listeners", "prop", "next", "event", "slot", "attr", "prop", "element", "event"]}